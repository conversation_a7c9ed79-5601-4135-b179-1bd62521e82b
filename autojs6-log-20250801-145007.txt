14:47:46.776/V: 开始运行 [$cwd/main不自动重启.js].
14:47:46.824/D: 🚫 ROOT模式启动 - 立即禁用所有权限申请
14:47:46.825/D: ✅ 第一阶段权限禁用完成
14:47:46.826/D: ✅ 所有权限申请函数已被禁用
14:47:46.827/D: 在AutoJS环境中加载模块...
14:47:46.929/D: DeviceOperation.js 加载完成
14:47:46.944/D: hid.js 加载完成
14:47:47.041/D: OCR模块已加载，但跳过自动初始化以避免权限弹窗
14:47:47.042/D: === 添加OCR函数到全局作用域 ===
14:47:47.043/D: OCR.js 加载完成
14:47:47.138/D: 小红书API模块已加载
14:47:47.140/D: 小红书操作.js 加载完成（相对路径）
14:47:47.140/D: 小红书操作模块将使用JSON配置文件
14:47:47.141/D: 已将 开始小红书点赞操作 赋值到全局
14:47:47.141/D: 已将 小红书点赞操作入口 赋值到全局
14:47:47.142/D: 已将 主函数 赋值到全局
14:47:47.142/D: 小红书操作模块中没有获取配置项函数，尝试其他方法获取
14:47:47.154/D: ErrorHandler.js 加载完成
14:47:47.240/D: OCR模块已加载，但跳过自动初始化以避免权限弹窗
14:47:47.240/D: === 添加OCR函数到全局作用域 ===
14:47:47.294/D: 小红书API模块已加载
14:47:47.295/D: flows.js 加载完成（相对路径）
14:47:47.296/D: 开始映射flows模块函数到全局...
14:47:47.296/D: ✅ 已将 重启飞行模式 映射到全局
14:47:47.296/D: ✅ 已将 备份工具箱还原备份 映射到全局
14:47:47.296/D: ✅ 已将 改机工具_阿帕奇 映射到全局
14:47:47.297/D: ✅ 已将 备份还原 映射到全局
14:47:47.297/D: ✅ 已将 恢复出厂设置 映射到全局
14:47:47.297/D: ✅ 已将 删除第一个备份 映射到全局
14:47:47.297/D: ✅ flows模块函数映射完成
14:47:47.297/D: 所有模块加载成功
14:47:47.298/D: ✅ $ocr函数已被安全包装
14:47:48.299/D: 延迟执行主函数，确保模块加载完成...
14:47:48.300/D: ======== 小红书自动点赞脚本启动 ========
14:47:48.301/D: 操作系统: nubia nubia Z7 Max 双4G版, Android 12
14:47:48.306/D: 屏幕分辨率: 1080x2400
14:47:48.306/D: 检查模块加载状态：
14:47:48.307/D: DeviceOperation: 已加载
14:47:48.308/D: OCR: 已加载
14:47:48.309/D: ErrorHandler: 已加载
14:47:48.310/D: 检查ROOT权限...
14:47:48.398/D: 检测到ROOT权限(方法1)
14:47:48.399/D: ROOT权限检查结果: 已获取
14:47:48.400/D: ROOT模式下，完全跳过无障碍服务和所有权限申请
14:47:48.400/D: 测试ROOT截图功能...
14:47:48.982/D: ✅ ROOT截图测试成功
14:47:49.017/D: ✅ ROOT模式准备完成，无需任何权限申请
14:47:49.018/D: 当前操作模式: ROOT
14:47:49.018/D: 当前交互操作模式: ROOT
14:47:49.019/D: 当前窗口信息模式: ROOT
14:47:49.019/D: 最大错误记录数已设置为 100
14:47:49.019/D: 错误处理配置已更新: 最大连续错误=5, 最大错误记录数=100, 错误恢复策略=返回
14:47:49.020/D: 已注册exit事件处理器
14:47:49.020/D: 初始化完成，开始执行操作流程
14:47:49.020/D: 开始执行整个操作流程
14:47:49.020/D: 开始重启飞行模式...
14:47:49.021/D: 正在打开飞行模式...
14:47:49.859/D: ✅ 飞行模式已打开
14:47:49.862/D: 等待5秒...
14:47:54.863/D: 正在关闭飞行模式...
14:47:55.488/D: ✅ 飞行模式已关闭
14:47:55.489/D: 🎉 飞行模式重启完成
14:48:00.614/D: 备份工具箱删除备份
14:48:04.187/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:04.189/D: 参数：必须关键词="同意", 匹配关键词="不同意|个人信息保护提示", 匹配数量=3, 排除关键词=""
14:48:04.190/D: 使用缓存的XML内容
14:48:04.191/D: 开始获取界面 XML...
14:48:06.558/D: 成功获取 XML 文件，大小: 3084 字节
14:48:06.559/D: 开始解析XML获取所有文字...
14:48:06.562/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" c...
14:48:06.564/D: 找到bounds: bounds="[124,589][955,1777]"
14:48:06.565/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:06.567/D: 找到节点: <node index="0" text="个人信息保护提示" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="an...
14:48:06.569/D: 从text属性提取到文字: "个人信息保护提示"
14:48:06.569/D: 找到bounds: bounds="[363,688][715,747]"
14:48:06.570/D: 添加文字项: "个人信息保护提示" 坐标: [363,688][715,747]
14:48:06.574/D: 找到节点: <node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.wi...
14:48:06.577/D: 找到bounds: bounds="[168,747][911,1473]"
14:48:06.577/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:06.582/D: 找到节点: <node index="0" text="欢迎使用小红书！ &#10;我们将通过《用户协议》和《隐私政策》，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法律...
14:48:06.585/D: 从text属性提取到文字: "欢迎使用小红书！ &#10;我们将通过《用户协议》和《隐私政策》，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法律法规要求，采取各种安全措施来保护您的个人信息。 &#10;我们重视未成年人的个人信息保护，若您是未满18周岁的未成年人，请在监护人的指导下阅读并同意以上协议以及《未成年人个人信息保护规则》。 &#10;点击“同意”按钮，表示您已知情并同意以上协议和以下约定。 &#10;1.为了保障软件的安全运行和账户安全，我们会申请收集您的设备信息、IP地址、WLAN MAC地址。 &#10;2.上传或拍摄图片、视频，需要使用您的媒体影音、图片、视频、音频、相机、麦克风权限。 &#10;3.我们可能会申请位置权限，用于为您推荐您可能感兴趣的内容。 &#10;4.为了帮助您发现更多朋友，我们会申请通讯录权限。 &#10;5.我们尊重您的选择权，您可以访问、修改、删除您的个人信息并管理您的授权，我们也为您提供注销、投诉渠道。"
14:48:06.586/D: 找到bounds: bounds="[223,775][856,1473]"
14:48:06.587/D: 添加文字项: "欢迎使用小红书！ &#10;我们将通过《用户协议》和《隐私政策》，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法律法规要求，采取各种安全措施来保护您的个人信息。 &#10;我们重视未成年人的个人信息保护，若您是未满18周岁的未成年人，请在监护人的指导下阅读并同意以上协议以及《未成年人个人信息保护规则》。 &#10;点击“同意”按钮，表示您已知情并同意以上协议和以下约定。 &#10;1.为了保障软件的安全运行和账户安全，我们会申请收集您的设备信息、IP地址、WLAN MAC地址。 &#10;2.上传或拍摄图片、视频，需要使用您的媒体影音、图片、视频、音频、相机、麦克风权限。 &#10;3.我们可能会申请位置权限，用于为您推荐您可能感兴趣的内容。 &#10;4.为了帮助您发现更多朋友，我们会申请通讯录权限。 &#10;5.我们尊重您的选择权，您可以访问、修改、删除您的个人信息并管理您的授权，我们也为您提供注销、投诉渠道。" 坐标: [223,775][856,1473]
14:48:06.590/D: 找到节点: <node index="2" text="同意" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:06.592/D: 从text属性提取到文字: "同意"
14:48:06.593/D: 找到bounds: bounds="[223,1506][856,1611]"
14:48:06.594/D: 添加文字项: "同意" 坐标: [223,1506][856,1611]
14:48:06.597/D: 找到节点: <node index="3" text="不同意" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android...
14:48:06.598/D: 从text属性提取到文字: "不同意"
14:48:06.599/D: 找到bounds: bounds="[490,1644][589,1689]"
14:48:06.600/D: 添加文字项: "不同意" 坐标: [490,1644][589,1689]
14:48:06.602/D: XML解析成功，共提取到 4 个文字项
14:48:06.604/D: 提取到的所有文字: "个人信息保护提示", "欢迎使用小红书！ &#10;我们将通过《用户协议》和《隐私政策》，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法律法规要求，采取各种安全措施来保护您的个人信息。 &#10;我们重视未成年人的个人信息保护，若您是未满18周岁的未成年人，请在监护人的指导下阅读并同意以上协议以及《未成年人个人信息保护规则》。 &#10;点击“同意”按钮，表示您已知情并同意以上协议和以下约定。 &#10;1.为了保障软件的安全运行和账户安全，我们会申请收集您的设备信息、IP地址、WLAN MAC地址。 &#10;2.上传或拍摄图片、视频，需要使用您的媒体影音、图片、视频、音频、相机、麦克风权限。 &#10;3.我们可能会申请位置权限，用于为您推荐您可能感兴趣的内容。 &#10;4.为了帮助您发现更多朋友，我们会申请通讯录权限。 &#10;5.我们尊重您的选择权，您可以访问、修改、删除您的个人信息并管理您的授权，我们也为您提供注销、投诉渠道。", "同意", "不同意"
14:48:06.605/D: 文字项1: "个人信息保护提示" 坐标: [363,688][715,747]
14:48:06.605/D: 文字项2: "欢迎使用小红书！ &#10;我们将通过《用户协议》和《隐私政策》，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法律法规要求，采取各种安全措施来保护您的个人信息。 &#10;我们重视未成年人的个人信息保护，若您是未满18周岁的未成年人，请在监护人的指导下阅读并同意以上协议以及《未成年人个人信息保护规则》。 &#10;点击“同意”按钮，表示您已知情并同意以上协议和以下约定。 &#10;1.为了保障软件的安全运行和账户安全，我们会申请收集您的设备信息、IP地址、WLAN MAC地址。 &#10;2.上传或拍摄图片、视频，需要使用您的媒体影音、图片、视频、音频、相机、麦克风权限。 &#10;3.我们可能会申请位置权限，用于为您推荐您可能感兴趣的内容。 &#10;4.为了帮助您发现更多朋友，我们会申请通讯录权限。 &#10;5.我们尊重您的选择权，您可以访问、修改、删除您的个人信息并管理您的授权，我们也为您提供注销、投诉渠道。" 坐标: [223,775][856,1473]
14:48:06.606/D: 文字项3: "同意" 坐标: [223,1506][856,1611]
14:48:06.607/D: 文字项4: "不同意" 坐标: [490,1644][589,1689]
14:48:06.609/D: 匹配模式列表: ["不同意", "个人信息保护提示"]
14:48:06.610/D: 正在检查文字 "欢迎使用小红书！ &#10;我们将通过《用户协议》和《隐私政策》，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法律法规要求，采取各种安全措施来保护您的个人信息。 &#10;我们重视未成年人的个人信息保护，若您是未满18周岁的未成年人，请在监护人的指导下阅读并同意以上协议以及《未成年人个人信息保护规则》。 &#10;点击“同意”按钮，表示您已知情并同意以上协议和以下约定。 &#10;1.为了保障软件的安全运行和账户安全，我们会申请收集您的设备信息、IP地址、WLAN MAC地址。 &#10;2.上传或拍摄图片、视频，需要使用您的媒体影音、图片、视频、音频、相机、麦克风权限。 &#10;3.我们可能会申请位置权限，用于为您推荐您可能感兴趣的内容。 &#10;4.为了帮助您发现更多朋友，我们会申请通讯录权限。 &#10;5.我们尊重您的选择权，您可以访问、修改、删除您的个人信息并管理您的授权，我们也为您提供注销、投诉渠道。" 是否匹配模式...
14:48:06.611/D:   模式 "不同意" 匹配结果: false
14:48:06.612/D:   模式 "个人信息保护提示" 匹配结果: false
14:48:06.614/D: ✅ 匹配到文字: "欢迎使用小红书！ &#10;我们将通过《用户协议》和《隐私政策》，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法律法规要求，采取各种安全措施来保护您的个人信息。 &#10;我们重视未成年人的个人信息保护，若您是未满18周岁的未成年人，请在监护人的指导下阅读并同意以上协议以及《未成年人个人信息保护规则》。 &#10;点击“同意”按钮，表示您已知情并同意以上协议和以下约定。 &#10;1.为了保障软件的安全运行和账户安全，我们会申请收集您的设备信息、IP地址、WLAN MAC地址。 &#10;2.上传或拍摄图片、视频，需要使用您的媒体影音、图片、视频、音频、相机、麦克风权限。 &#10;3.我们可能会申请位置权限，用于为您推荐您可能感兴趣的内容。 &#10;4.为了帮助您发现更多朋友，我们会申请通讯录权限。 &#10;5.我们尊重您的选择权，您可以访问、修改、删除您的个人信息并管理您的授权，我们也为您提供注销、投诉渠道。" 坐标: [223,775][856,1473] 原因: 必须关键词
14:48:06.614/D: 正在检查文字 "同意" 是否匹配模式...
14:48:06.615/D:   模式 "不同意" 匹配结果: false
14:48:06.616/D:   模式 "个人信息保护提示" 匹配结果: false
14:48:06.617/D: ✅ 匹配到文字: "同意" 坐标: [223,1506][856,1611] 原因: 必须关键词
14:48:06.618/D: 正在检查文字 "不同意" 是否匹配模式...
14:48:06.619/D:   模式 "不同意" 匹配结果: true
14:48:06.621/D: ✅ 匹配到文字: "不同意" 坐标: [490,1644][589,1689] 原因: 必须关键词 + 匹配关键词
14:48:06.622/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 3 个结果====🔚🔚🔚
14:48:06.622/D: 🔛🔛🔛====点击XML关键词开始====🔛🔛🔛
14:48:06.623/D: 要点击的关键词模式: "同意"
14:48:06.625/D: ✅ 找到匹配项: "同意" 匹配模式: "同意" 匹配类型: 精确匹配
14:48:06.632/D: 🎯 准备点击: "同意" 坐标: (540, 1559) 偏移: (0, 0) 次数: 1 间隔: 50ms
14:48:06.633/D: su -c 'input tap 537.5 1555.5'
14:48:06.707/D: ✅ 点击执行完成
14:48:06.707/D: 🔚🔚🔚====点击XML关键词结束====🔚🔚🔚
14:48:08.708/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:08.709/D: 参数：必须关键词="同意并继续", 匹配关键词="温馨提示|放弃使用", 匹配数量=3, 排除关键词=""
14:48:08.710/D: 获取新的XML内容
14:48:08.710/D: 开始获取界面 XML...
14:48:11.805/D: 成功获取 XML 文件，大小: 412 字节
14:48:11.806/D: 开始解析XML获取所有文字...
14:48:11.809/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" c...
14:48:11.815/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:11.816/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:11.817/D: XML解析成功，共提取到 0 个文字项
14:48:11.817/D: XML未提取到任何文字
14:48:11.818/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:11.818/D: 参数：必须关键词="同意并继续", 匹配关键词="温馨提示|放弃使用", 匹配数量=3, 排除关键词=""
14:48:11.821/D: 获取新的XML内容
14:48:11.822/D: 开始获取界面 XML...
14:48:15.159/D: 成功获取 XML 文件，大小: 20955 字节
14:48:15.160/D: 开始解析XML获取所有文字...
14:48:15.162/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" c...
14:48:15.164/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:15.165/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.167/D: 找到节点: <node index="0" text="跳过" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.169/D: 从text属性提取到文字: "跳过"
14:48:15.170/D: 找到bounds: bounds="[949,129][1025,184]"
14:48:15.172/D: 添加文字项: "跳过" 坐标: [949,129][1025,184]
14:48:15.175/D: 找到节点: <node index="1" text="选择兴趣推荐更精准" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="a...
14:48:15.178/D: 从text属性提取到文字: "选择兴趣推荐更精准"
14:48:15.178/D: 找到bounds: bounds="[293,275][788,353]"
14:48:15.180/D: 添加文字项: "选择兴趣推荐更精准" 坐标: [293,275][788,353]
14:48:15.183/D: 找到节点: <node index="2" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androidx.r...
14:48:15.185/D: 找到bounds: bounds="[0,375][1080,2058]"
14:48:15.186/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.188/D: 找到节点: <node index="0" text="摄影" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.190/D: 从text属性提取到文字: "摄影"
14:48:15.191/D: 找到bounds: bounds="[97,419][1014,474]"
14:48:15.193/D: 添加文字项: "摄影" 坐标: [97,419][1014,474]
14:48:15.196/D: 找到节点: <node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:15.198/D: 找到bounds: bounds="[66,507][295,606]"
14:48:15.199/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.201/D: 找到节点: <node index="0" text="摄影作品" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.202/D: 从text属性提取到文字: "摄影作品"
14:48:15.202/D: 找到bounds: bounds="[110,507][262,606]"
14:48:15.203/D: 添加文字项: "摄影作品" 坐标: [110,507][262,606]
14:48:15.204/D: 找到节点: <node index="2" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:15.205/D: 找到bounds: bounds="[328,507][557,606]"
14:48:15.205/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.206/D: 找到节点: <node index="0" text="拍照技巧" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.206/D: 从text属性提取到文字: "拍照技巧"
14:48:15.207/D: 找到bounds: bounds="[372,507][524,606]"
14:48:15.207/D: 添加文字项: "拍照技巧" 坐标: [372,507][524,606]
14:48:15.208/D: 找到节点: <node index="3" text="兴趣爱好" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.209/D: 从text属性提取到文字: "兴趣爱好"
14:48:15.209/D: 找到bounds: bounds="[97,661][1014,716]"
14:48:15.210/D: 添加文字项: "兴趣爱好" 坐标: [97,661][1014,716]
14:48:15.211/D: 找到节点: <node index="4" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:15.212/D: 找到bounds: bounds="[66,749][219,848]"
14:48:15.213/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.213/D: 找到节点: <node index="0" text="绘画" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.214/D: 从text属性提取到文字: "绘画"
14:48:15.214/D: 找到bounds: bounds="[110,749][186,848]"
14:48:15.215/D: 添加文字项: "绘画" 坐标: [110,749][186,848]
14:48:15.216/D: 找到节点: <node index="5" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:15.217/D: 找到bounds: bounds="[252,749][569,848]"
14:48:15.218/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.218/D: 找到节点: <node index="0" text="毛绒&amp;DIY玩具" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class...
14:48:15.219/D: 从text属性提取到文字: "毛绒&amp;DIY玩具"
14:48:15.220/D: 找到bounds: bounds="[296,749][536,848]"
14:48:15.220/D: 添加文字项: "毛绒&amp;DIY玩具" 坐标: [296,749][536,848]
14:48:15.222/D: 找到节点: <node index="6" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:15.223/D: 找到bounds: bounds="[602,749][755,848]"
14:48:15.223/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.224/D: 找到节点: <node index="0" text="手工" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.225/D: 从text属性提取到文字: "手工"
14:48:15.225/D: 找到bounds: bounds="[646,749][722,848]"
14:48:15.226/D: 添加文字项: "手工" 坐标: [646,749][722,848]
14:48:15.227/D: 找到节点: <node index="7" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:15.228/D: 找到bounds: bounds="[66,870][295,969]"
14:48:15.229/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.229/D: 找到节点: <node index="0" text="文具手帐" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.230/D: 从text属性提取到文字: "文具手帐"
14:48:15.231/D: 找到bounds: bounds="[110,870][262,969]"
14:48:15.231/D: 添加文字项: "文具手帐" 坐标: [110,870][262,969]
14:48:15.232/D: 找到节点: <node index="8" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:15.233/D: 找到bounds: bounds="[328,870][557,969]"
14:48:15.233/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.234/D: 找到节点: <node index="0" text="花艺绿植" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.234/D: 从text属性提取到文字: "花艺绿植"
14:48:15.235/D: 找到bounds: bounds="[372,870][524,969]"
14:48:15.235/D: 添加文字项: "花艺绿植" 坐标: [372,870][524,969]
14:48:15.236/D: 找到节点: <node index="9" text="知识学习" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.237/D: 从text属性提取到文字: "知识学习"
14:48:15.237/D: 找到bounds: bounds="[97,1024][1014,1079]"
14:48:15.238/D: 添加文字项: "知识学习" 坐标: [97,1024][1014,1079]
14:48:15.239/D: 找到节点: <node index="10" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.239/D: 找到bounds: bounds="[66,1112][219,1211]"
14:48:15.240/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.240/D: 找到节点: <node index="0" text="英语" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.241/D: 从text属性提取到文字: "英语"
14:48:15.241/D: 找到bounds: bounds="[110,1112][186,1211]"
14:48:15.242/D: 添加文字项: "英语" 坐标: [110,1112][186,1211]
14:48:15.243/D: 找到节点: <node index="11" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.243/D: 找到bounds: bounds="[252,1112][481,1211]"
14:48:15.244/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.244/D: 找到节点: <node index="0" text="书籍推荐" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.245/D: 从text属性提取到文字: "书籍推荐"
14:48:15.245/D: 找到bounds: bounds="[296,1112][448,1211]"
14:48:15.245/D: 添加文字项: "书籍推荐" 坐标: [296,1112][448,1211]
14:48:15.246/D: 找到节点: <node index="12" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.247/D: 找到bounds: bounds="[514,1112][705,1211]"
14:48:15.247/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.248/D: 找到节点: <node index="0" text="心理学" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android...
14:48:15.248/D: 从text属性提取到文字: "心理学"
14:48:15.249/D: 找到bounds: bounds="[558,1112][672,1211]"
14:48:15.249/D: 添加文字项: "心理学" 坐标: [558,1112][672,1211]
14:48:15.250/D: 找到节点: <node index="13" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.251/D: 找到bounds: bounds="[738,1112][967,1211]"
14:48:15.251/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.252/D: 找到节点: <node index="0" text="金融财经" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.252/D: 从text属性提取到文字: "金融财经"
14:48:15.253/D: 找到bounds: bounds="[782,1112][934,1211]"
14:48:15.253/D: 添加文字项: "金融财经" 坐标: [782,1112][934,1211]
14:48:15.254/D: 找到节点: <node index="14" text="影视娱乐" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andro...
14:48:15.255/D: 从text属性提取到文字: "影视娱乐"
14:48:15.255/D: 找到bounds: bounds="[97,1266][1014,1321]"
14:48:15.255/D: 添加文字项: "影视娱乐" 坐标: [97,1266][1014,1321]
14:48:15.257/D: 找到节点: <node index="15" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.258/D: 找到bounds: bounds="[66,1354][219,1453]"
14:48:15.258/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.259/D: 找到节点: <node index="0" text="美女" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.260/D: 从text属性提取到文字: "美女"
14:48:15.261/D: 找到bounds: bounds="[110,1354][186,1453]"
14:48:15.261/D: 添加文字项: "美女" 坐标: [110,1354][186,1453]
14:48:15.262/D: 找到节点: <node index="16" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.263/D: 找到bounds: bounds="[252,1354][460,1453]"
14:48:15.264/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.264/D: 找到节点: <node index="0" text="明星" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.265/D: 从text属性提取到文字: "明星"
14:48:15.266/D: 找到bounds: bounds="[351,1354][427,1453]"
14:48:15.266/D: 添加文字项: "明星" 坐标: [351,1354][427,1453]
14:48:15.267/D: 找到节点: <node index="17" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.268/D: 找到bounds: bounds="[493,1354][646,1453]"
14:48:15.269/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.270/D: 找到节点: <node index="0" text="帅哥" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.271/D: 从text属性提取到文字: "帅哥"
14:48:15.271/D: 找到bounds: bounds="[537,1354][613,1453]"
14:48:15.272/D: 添加文字项: "帅哥" 坐标: [537,1354][613,1453]
14:48:15.273/D: 找到节点: <node index="18" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.274/D: 找到bounds: bounds="[679,1354][908,1453]"
14:48:15.275/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.275/D: 找到节点: <node index="0" text="网文小说" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.276/D: 从text属性提取到文字: "网文小说"
14:48:15.277/D: 找到bounds: bounds="[723,1354][875,1453]"
14:48:15.277/D: 添加文字项: "网文小说" 坐标: [723,1354][875,1453]
14:48:15.279/D: 找到节点: <node index="19" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.280/D: 找到bounds: bounds="[66,1475][295,1574]"
14:48:15.280/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.281/D: 找到节点: <node index="0" text="搞笑段子" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:15.282/D: 从text属性提取到文字: "搞笑段子"
14:48:15.282/D: 找到bounds: bounds="[110,1475][262,1574]"
14:48:15.283/D: 添加文字项: "搞笑段子" 坐标: [110,1475][262,1574]
14:48:15.284/D: 找到节点: <node index="20" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.285/D: 找到bounds: bounds="[328,1475][481,1574]"
14:48:15.285/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.286/D: 找到节点: <node index="0" text="短剧" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.287/D: 从text属性提取到文字: "短剧"
14:48:15.287/D: 找到bounds: bounds="[372,1475][448,1574]"
14:48:15.287/D: 添加文字项: "短剧" 坐标: [372,1475][448,1574]
14:48:15.288/D: 找到节点: <node index="21" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.289/D: 找到bounds: bounds="[514,1475][667,1574]"
14:48:15.289/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.290/D: 找到节点: <node index="0" text="综艺" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.291/D: 从text属性提取到文字: "综艺"
14:48:15.291/D: 找到bounds: bounds="[558,1475][634,1574]"
14:48:15.291/D: 添加文字项: "综艺" 坐标: [558,1475][634,1574]
14:48:15.293/D: 找到节点: <node index="22" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.293/D: 找到bounds: bounds="[700,1475][891,1574]"
14:48:15.294/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.294/D: 找到节点: <node index="0" text="电视剧" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android...
14:48:15.295/D: 从text属性提取到文字: "电视剧"
14:48:15.295/D: 找到bounds: bounds="[744,1475][858,1574]"
14:48:15.296/D: 添加文字项: "电视剧" 坐标: [744,1475][858,1574]
14:48:15.297/D: 找到节点: <node index="23" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.297/D: 找到bounds: bounds="[66,1596][219,1695]"
14:48:15.298/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.298/D: 找到节点: <node index="0" text="电影" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.299/D: 从text属性提取到文字: "电影"
14:48:15.299/D: 找到bounds: bounds="[110,1596][186,1695]"
14:48:15.299/D: 添加文字项: "电影" 坐标: [110,1596][186,1695]
14:48:15.300/D: 找到节点: <node index="24" text="生活记录" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andro...
14:48:15.301/D: 从text属性提取到文字: "生活记录"
14:48:15.301/D: 找到bounds: bounds="[97,1750][1014,1805]"
14:48:15.301/D: 添加文字项: "生活记录" 坐标: [97,1750][1014,1805]
14:48:15.302/D: 找到节点: <node index="25" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.302/D: 找到bounds: bounds="[66,1838][298,1937]"
14:48:15.303/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.303/D: 找到节点: <node index="0" text="Vlog记录" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andr...
14:48:15.304/D: 从text属性提取到文字: "Vlog记录"
14:48:15.304/D: 找到bounds: bounds="[110,1838][265,1937]"
14:48:15.304/D: 添加文字项: "Vlog记录" 坐标: [110,1838][265,1937]
14:48:15.305/D: 找到节点: <node index="26" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:15.306/D: 找到bounds: bounds="[331,1838][539,1937]"
14:48:15.306/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.306/D: 找到节点: <node index="0" text="萌宠" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:15.307/D: 从text属性提取到文字: "萌宠"
14:48:15.307/D: 找到bounds: bounds="[430,1838][506,1937]"
14:48:15.308/D: 添加文字项: "萌宠" 坐标: [430,1838][506,1937]
14:48:15.308/D: 找到节点: <node index="27" text="运动" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android...
14:48:15.309/D: 从text属性提取到文字: "运动"
14:48:15.309/D: 找到bounds: bounds="[97,1992][1014,2047]"
14:48:15.310/D: 添加文字项: "运动" 坐标: [97,1992][1014,2047]
14:48:15.311/D: 找到节点: <node index="3" text="选择4个兴趣" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andr...
14:48:15.311/D: 从text属性提取到文字: "选择4个兴趣"
14:48:15.312/D: 找到bounds: bounds="[44,2075][1036,2174]"
14:48:15.312/D: 添加文字项: "选择4个兴趣" 坐标: [44,2075][1036,2174]
14:48:15.313/D: 找到节点: <node index="4" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.wi...
14:48:15.313/D: 找到bounds: bounds="[0,0][0,0]"
14:48:15.314/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:15.314/D: 找到节点: <node index="0" text="已有账号，去登录" resource-id="" class="android.widget.TextView" package="com.xingin.x...
14:48:15.315/D: 从text属性提取到文字: "已有账号，去登录"
14:48:15.315/D: 找到bounds: bounds="[0,0][0,0]"
14:48:15.316/D: 添加文字项: "已有账号，去登录" 坐标: [0,0][0,0]
14:48:15.316/D: XML解析成功，共提取到 32 个文字项
14:48:15.317/D: 提取到的所有文字: "跳过", "选择兴趣推荐更精准", "摄影", "摄影作品", "拍照技巧", "兴趣爱好", "绘画", "毛绒&amp;DIY玩具", "手工", "文具手帐", "花艺绿植", "知识学习", "英语", "书籍推荐", "心理学", "金融财经", "影视娱乐", "美女", "明星", "帅哥", "网文小说", "搞笑段子", "短剧", "综艺", "电视剧", "电影", "生活记录", "Vlog记录", "萌宠", "运动", "选择4个兴趣", "已有账号，去登录"
14:48:15.317/D: 文字项1: "跳过" 坐标: [949,129][1025,184]
14:48:15.318/D: 文字项2: "选择兴趣推荐更精准" 坐标: [293,275][788,353]
14:48:15.318/D: 文字项3: "摄影" 坐标: [97,419][1014,474]
14:48:15.319/D: 文字项4: "摄影作品" 坐标: [110,507][262,606]
14:48:15.319/D: 文字项5: "拍照技巧" 坐标: [372,507][524,606]
14:48:15.319/D: 文字项6: "兴趣爱好" 坐标: [97,661][1014,716]
14:48:15.320/D: 文字项7: "绘画" 坐标: [110,749][186,848]
14:48:15.320/D: 文字项8: "毛绒&amp;DIY玩具" 坐标: [296,749][536,848]
14:48:15.320/D: 文字项9: "手工" 坐标: [646,749][722,848]
14:48:15.321/D: 文字项10: "文具手帐" 坐标: [110,870][262,969]
14:48:15.321/D: 文字项11: "花艺绿植" 坐标: [372,870][524,969]
14:48:15.321/D: 文字项12: "知识学习" 坐标: [97,1024][1014,1079]
14:48:15.322/D: 文字项13: "英语" 坐标: [110,1112][186,1211]
14:48:15.322/D: 文字项14: "书籍推荐" 坐标: [296,1112][448,1211]
14:48:15.323/D: 文字项15: "心理学" 坐标: [558,1112][672,1211]
14:48:15.323/D: 文字项16: "金融财经" 坐标: [782,1112][934,1211]
14:48:15.324/D: 文字项17: "影视娱乐" 坐标: [97,1266][1014,1321]
14:48:15.324/D: 文字项18: "美女" 坐标: [110,1354][186,1453]
14:48:15.325/D: 文字项19: "明星" 坐标: [351,1354][427,1453]
14:48:15.325/D: 文字项20: "帅哥" 坐标: [537,1354][613,1453]
14:48:15.325/D: 文字项21: "网文小说" 坐标: [723,1354][875,1453]
14:48:15.326/D: 文字项22: "搞笑段子" 坐标: [110,1475][262,1574]
14:48:15.326/D: 文字项23: "短剧" 坐标: [372,1475][448,1574]
14:48:15.326/D: 文字项24: "综艺" 坐标: [558,1475][634,1574]
14:48:15.327/D: 文字项25: "电视剧" 坐标: [744,1475][858,1574]
14:48:15.327/D: 文字项26: "电影" 坐标: [110,1596][186,1695]
14:48:15.327/D: 文字项27: "生活记录" 坐标: [97,1750][1014,1805]
14:48:15.328/D: 文字项28: "Vlog记录" 坐标: [110,1838][265,1937]
14:48:15.328/D: 文字项29: "萌宠" 坐标: [430,1838][506,1937]
14:48:15.328/D: 文字项30: "运动" 坐标: [97,1992][1014,2047]
14:48:15.328/D: 文字项31: "选择4个兴趣" 坐标: [44,2075][1036,2174]
14:48:15.329/D: 文字项32: "已有账号，去登录" 坐标: [0,0][0,0]
14:48:15.329/D: 匹配模式列表: ["温馨提示", "放弃使用"]
14:48:15.331/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:15.332/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:15.332/D: 参数：必须关键词="跳过", 匹配关键词="已有账号，去登录|选择*个兴趣", 匹配数量=2, 排除关键词=""
14:48:15.333/D: 使用缓存的XML内容
14:48:15.333/D: 开始获取界面 XML...
14:48:17.694/D: 成功获取 XML 文件，大小: 20955 字节
14:48:17.695/D: 开始解析XML获取所有文字...
14:48:17.696/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" c...
14:48:17.696/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:17.697/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.697/D: 找到节点: <node index="0" text="跳过" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.698/D: 从text属性提取到文字: "跳过"
14:48:17.698/D: 找到bounds: bounds="[949,129][1025,184]"
14:48:17.699/D: 添加文字项: "跳过" 坐标: [949,129][1025,184]
14:48:17.700/D: 找到节点: <node index="1" text="选择兴趣推荐更精准" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="a...
14:48:17.700/D: 从text属性提取到文字: "选择兴趣推荐更精准"
14:48:17.701/D: 找到bounds: bounds="[293,275][788,353]"
14:48:17.701/D: 添加文字项: "选择兴趣推荐更精准" 坐标: [293,275][788,353]
14:48:17.702/D: 找到节点: <node index="2" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androidx.r...
14:48:17.703/D: 找到bounds: bounds="[0,375][1080,2058]"
14:48:17.703/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.703/D: 找到节点: <node index="0" text="摄影" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.704/D: 从text属性提取到文字: "摄影"
14:48:17.704/D: 找到bounds: bounds="[97,419][1014,474]"
14:48:17.705/D: 添加文字项: "摄影" 坐标: [97,419][1014,474]
14:48:17.706/D: 找到节点: <node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:17.706/D: 找到bounds: bounds="[66,507][295,606]"
14:48:17.706/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.707/D: 找到节点: <node index="0" text="摄影作品" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.707/D: 从text属性提取到文字: "摄影作品"
14:48:17.708/D: 找到bounds: bounds="[110,507][262,606]"
14:48:17.708/D: 添加文字项: "摄影作品" 坐标: [110,507][262,606]
14:48:17.709/D: 找到节点: <node index="2" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:17.709/D: 找到bounds: bounds="[328,507][557,606]"
14:48:17.709/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.710/D: 找到节点: <node index="0" text="拍照技巧" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.710/D: 从text属性提取到文字: "拍照技巧"
14:48:17.710/D: 找到bounds: bounds="[372,507][524,606]"
14:48:17.711/D: 添加文字项: "拍照技巧" 坐标: [372,507][524,606]
14:48:17.712/D: 找到节点: <node index="3" text="兴趣爱好" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.712/D: 从text属性提取到文字: "兴趣爱好"
14:48:17.712/D: 找到bounds: bounds="[97,661][1014,716]"
14:48:17.713/D: 添加文字项: "兴趣爱好" 坐标: [97,661][1014,716]
14:48:17.714/D: 找到节点: <node index="4" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:17.714/D: 找到bounds: bounds="[66,749][219,848]"
14:48:17.715/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.715/D: 找到节点: <node index="0" text="绘画" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.716/D: 从text属性提取到文字: "绘画"
14:48:17.716/D: 找到bounds: bounds="[110,749][186,848]"
14:48:17.716/D: 添加文字项: "绘画" 坐标: [110,749][186,848]
14:48:17.717/D: 找到节点: <node index="5" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:17.718/D: 找到bounds: bounds="[252,749][569,848]"
14:48:17.718/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.718/D: 找到节点: <node index="0" text="毛绒&amp;DIY玩具" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class...
14:48:17.719/D: 从text属性提取到文字: "毛绒&amp;DIY玩具"
14:48:17.719/D: 找到bounds: bounds="[296,749][536,848]"
14:48:17.719/D: 添加文字项: "毛绒&amp;DIY玩具" 坐标: [296,749][536,848]
14:48:17.720/D: 找到节点: <node index="6" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:17.721/D: 找到bounds: bounds="[602,749][755,848]"
14:48:17.721/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.721/D: 找到节点: <node index="0" text="手工" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.722/D: 从text属性提取到文字: "手工"
14:48:17.722/D: 找到bounds: bounds="[646,749][722,848]"
14:48:17.723/D: 添加文字项: "手工" 坐标: [646,749][722,848]
14:48:17.723/D: 找到节点: <node index="7" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:17.724/D: 找到bounds: bounds="[66,870][295,969]"
14:48:17.724/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.725/D: 找到节点: <node index="0" text="文具手帐" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.725/D: 从text属性提取到文字: "文具手帐"
14:48:17.726/D: 找到bounds: bounds="[110,870][262,969]"
14:48:17.726/D: 添加文字项: "文具手帐" 坐标: [110,870][262,969]
14:48:17.727/D: 找到节点: <node index="8" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:17.727/D: 找到bounds: bounds="[328,870][557,969]"
14:48:17.728/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.728/D: 找到节点: <node index="0" text="花艺绿植" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.729/D: 从text属性提取到文字: "花艺绿植"
14:48:17.729/D: 找到bounds: bounds="[372,870][524,969]"
14:48:17.729/D: 添加文字项: "花艺绿植" 坐标: [372,870][524,969]
14:48:17.730/D: 找到节点: <node index="9" text="知识学习" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.730/D: 从text属性提取到文字: "知识学习"
14:48:17.730/D: 找到bounds: bounds="[97,1024][1014,1079]"
14:48:17.731/D: 添加文字项: "知识学习" 坐标: [97,1024][1014,1079]
14:48:17.732/D: 找到节点: <node index="10" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.732/D: 找到bounds: bounds="[66,1112][219,1211]"
14:48:17.733/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.733/D: 找到节点: <node index="0" text="英语" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.734/D: 从text属性提取到文字: "英语"
14:48:17.734/D: 找到bounds: bounds="[110,1112][186,1211]"
14:48:17.734/D: 添加文字项: "英语" 坐标: [110,1112][186,1211]
14:48:17.735/D: 找到节点: <node index="11" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.736/D: 找到bounds: bounds="[252,1112][481,1211]"
14:48:17.736/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.736/D: 找到节点: <node index="0" text="书籍推荐" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.737/D: 从text属性提取到文字: "书籍推荐"
14:48:17.737/D: 找到bounds: bounds="[296,1112][448,1211]"
14:48:17.738/D: 添加文字项: "书籍推荐" 坐标: [296,1112][448,1211]
14:48:17.738/D: 找到节点: <node index="12" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.739/D: 找到bounds: bounds="[514,1112][705,1211]"
14:48:17.739/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.740/D: 找到节点: <node index="0" text="心理学" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android...
14:48:17.740/D: 从text属性提取到文字: "心理学"
14:48:17.740/D: 找到bounds: bounds="[558,1112][672,1211]"
14:48:17.741/D: 添加文字项: "心理学" 坐标: [558,1112][672,1211]
14:48:17.741/D: 找到节点: <node index="13" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.742/D: 找到bounds: bounds="[738,1112][967,1211]"
14:48:17.742/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.743/D: 找到节点: <node index="0" text="金融财经" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.743/D: 从text属性提取到文字: "金融财经"
14:48:17.744/D: 找到bounds: bounds="[782,1112][934,1211]"
14:48:17.744/D: 添加文字项: "金融财经" 坐标: [782,1112][934,1211]
14:48:17.744/D: 找到节点: <node index="14" text="影视娱乐" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andro...
14:48:17.745/D: 从text属性提取到文字: "影视娱乐"
14:48:17.745/D: 找到bounds: bounds="[97,1266][1014,1321]"
14:48:17.745/D: 添加文字项: "影视娱乐" 坐标: [97,1266][1014,1321]
14:48:17.746/D: 找到节点: <node index="15" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.747/D: 找到bounds: bounds="[66,1354][219,1453]"
14:48:17.747/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.748/D: 找到节点: <node index="0" text="美女" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.748/D: 从text属性提取到文字: "美女"
14:48:17.748/D: 找到bounds: bounds="[110,1354][186,1453]"
14:48:17.749/D: 添加文字项: "美女" 坐标: [110,1354][186,1453]
14:48:17.749/D: 找到节点: <node index="16" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.750/D: 找到bounds: bounds="[252,1354][460,1453]"
14:48:17.750/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.750/D: 找到节点: <node index="0" text="明星" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.751/D: 从text属性提取到文字: "明星"
14:48:17.751/D: 找到bounds: bounds="[351,1354][427,1453]"
14:48:17.752/D: 添加文字项: "明星" 坐标: [351,1354][427,1453]
14:48:17.752/D: 找到节点: <node index="17" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.753/D: 找到bounds: bounds="[493,1354][646,1453]"
14:48:17.753/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.754/D: 找到节点: <node index="0" text="帅哥" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.754/D: 从text属性提取到文字: "帅哥"
14:48:17.755/D: 找到bounds: bounds="[537,1354][613,1453]"
14:48:17.755/D: 添加文字项: "帅哥" 坐标: [537,1354][613,1453]
14:48:17.756/D: 找到节点: <node index="18" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.756/D: 找到bounds: bounds="[679,1354][908,1453]"
14:48:17.756/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.757/D: 找到节点: <node index="0" text="网文小说" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.757/D: 从text属性提取到文字: "网文小说"
14:48:17.758/D: 找到bounds: bounds="[723,1354][875,1453]"
14:48:17.758/D: 添加文字项: "网文小说" 坐标: [723,1354][875,1453]
14:48:17.759/D: 找到节点: <node index="19" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.759/D: 找到bounds: bounds="[66,1475][295,1574]"
14:48:17.759/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.760/D: 找到节点: <node index="0" text="搞笑段子" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:17.760/D: 从text属性提取到文字: "搞笑段子"
14:48:17.760/D: 找到bounds: bounds="[110,1475][262,1574]"
14:48:17.761/D: 添加文字项: "搞笑段子" 坐标: [110,1475][262,1574]
14:48:17.761/D: 找到节点: <node index="20" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.762/D: 找到bounds: bounds="[328,1475][481,1574]"
14:48:17.762/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.762/D: 找到节点: <node index="0" text="短剧" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.763/D: 从text属性提取到文字: "短剧"
14:48:17.763/D: 找到bounds: bounds="[372,1475][448,1574]"
14:48:17.763/D: 添加文字项: "短剧" 坐标: [372,1475][448,1574]
14:48:17.764/D: 找到节点: <node index="21" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.765/D: 找到bounds: bounds="[514,1475][667,1574]"
14:48:17.765/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.765/D: 找到节点: <node index="0" text="综艺" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.766/D: 从text属性提取到文字: "综艺"
14:48:17.766/D: 找到bounds: bounds="[558,1475][634,1574]"
14:48:17.766/D: 添加文字项: "综艺" 坐标: [558,1475][634,1574]
14:48:17.767/D: 找到节点: <node index="22" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.768/D: 找到bounds: bounds="[700,1475][891,1574]"
14:48:17.768/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.769/D: 找到节点: <node index="0" text="电视剧" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android...
14:48:17.769/D: 从text属性提取到文字: "电视剧"
14:48:17.770/D: 找到bounds: bounds="[744,1475][858,1574]"
14:48:17.770/D: 添加文字项: "电视剧" 坐标: [744,1475][858,1574]
14:48:17.771/D: 找到节点: <node index="23" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.771/D: 找到bounds: bounds="[66,1596][219,1695]"
14:48:17.771/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.772/D: 找到节点: <node index="0" text="电影" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.772/D: 从text属性提取到文字: "电影"
14:48:17.773/D: 找到bounds: bounds="[110,1596][186,1695]"
14:48:17.773/D: 添加文字项: "电影" 坐标: [110,1596][186,1695]
14:48:17.773/D: 找到节点: <node index="24" text="生活记录" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andro...
14:48:17.774/D: 从text属性提取到文字: "生活记录"
14:48:17.774/D: 找到bounds: bounds="[97,1750][1014,1805]"
14:48:17.774/D: 添加文字项: "生活记录" 坐标: [97,1750][1014,1805]
14:48:17.775/D: 找到节点: <node index="25" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.776/D: 找到bounds: bounds="[66,1838][298,1937]"
14:48:17.776/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.776/D: 找到节点: <node index="0" text="Vlog记录" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andr...
14:48:17.777/D: 从text属性提取到文字: "Vlog记录"
14:48:17.777/D: 找到bounds: bounds="[110,1838][265,1937]"
14:48:17.777/D: 添加文字项: "Vlog记录" 坐标: [110,1838][265,1937]
14:48:17.778/D: 找到节点: <node index="26" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.v...
14:48:17.778/D: 找到bounds: bounds="[331,1838][539,1937]"
14:48:17.779/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.779/D: 找到节点: <node index="0" text="萌宠" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:17.780/D: 从text属性提取到文字: "萌宠"
14:48:17.780/D: 找到bounds: bounds="[430,1838][506,1937]"
14:48:17.780/D: 添加文字项: "萌宠" 坐标: [430,1838][506,1937]
14:48:17.781/D: 找到节点: <node index="27" text="运动" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android...
14:48:17.781/D: 从text属性提取到文字: "运动"
14:48:17.782/D: 找到bounds: bounds="[97,1992][1014,2047]"
14:48:17.782/D: 添加文字项: "运动" 坐标: [97,1992][1014,2047]
14:48:17.783/D: 找到节点: <node index="3" text="选择4个兴趣" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andr...
14:48:17.783/D: 从text属性提取到文字: "选择4个兴趣"
14:48:17.783/D: 找到bounds: bounds="[44,2075][1036,2174]"
14:48:17.784/D: 添加文字项: "选择4个兴趣" 坐标: [44,2075][1036,2174]
14:48:17.784/D: 找到节点: <node index="4" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.wi...
14:48:17.785/D: 找到bounds: bounds="[0,0][0,0]"
14:48:17.785/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:17.785/D: 找到节点: <node index="0" text="已有账号，去登录" resource-id="" class="android.widget.TextView" package="com.xingin.x...
14:48:17.786/D: 从text属性提取到文字: "已有账号，去登录"
14:48:17.786/D: 找到bounds: bounds="[0,0][0,0]"
14:48:17.786/D: 添加文字项: "已有账号，去登录" 坐标: [0,0][0,0]
14:48:17.787/D: XML解析成功，共提取到 32 个文字项
14:48:17.787/D: 提取到的所有文字: "跳过", "选择兴趣推荐更精准", "摄影", "摄影作品", "拍照技巧", "兴趣爱好", "绘画", "毛绒&amp;DIY玩具", "手工", "文具手帐", "花艺绿植", "知识学习", "英语", "书籍推荐", "心理学", "金融财经", "影视娱乐", "美女", "明星", "帅哥", "网文小说", "搞笑段子", "短剧", "综艺", "电视剧", "电影", "生活记录", "Vlog记录", "萌宠", "运动", "选择4个兴趣", "已有账号，去登录"
14:48:17.788/D: 文字项1: "跳过" 坐标: [949,129][1025,184]
14:48:17.788/D: 文字项2: "选择兴趣推荐更精准" 坐标: [293,275][788,353]
14:48:17.788/D: 文字项3: "摄影" 坐标: [97,419][1014,474]
14:48:17.788/D: 文字项4: "摄影作品" 坐标: [110,507][262,606]
14:48:17.789/D: 文字项5: "拍照技巧" 坐标: [372,507][524,606]
14:48:17.789/D: 文字项6: "兴趣爱好" 坐标: [97,661][1014,716]
14:48:17.789/D: 文字项7: "绘画" 坐标: [110,749][186,848]
14:48:17.789/D: 文字项8: "毛绒&amp;DIY玩具" 坐标: [296,749][536,848]
14:48:17.790/D: 文字项9: "手工" 坐标: [646,749][722,848]
14:48:17.790/D: 文字项10: "文具手帐" 坐标: [110,870][262,969]
14:48:17.790/D: 文字项11: "花艺绿植" 坐标: [372,870][524,969]
14:48:17.790/D: 文字项12: "知识学习" 坐标: [97,1024][1014,1079]
14:48:17.791/D: 文字项13: "英语" 坐标: [110,1112][186,1211]
14:48:17.791/D: 文字项14: "书籍推荐" 坐标: [296,1112][448,1211]
14:48:17.791/D: 文字项15: "心理学" 坐标: [558,1112][672,1211]
14:48:17.792/D: 文字项16: "金融财经" 坐标: [782,1112][934,1211]
14:48:17.792/D: 文字项17: "影视娱乐" 坐标: [97,1266][1014,1321]
14:48:17.792/D: 文字项18: "美女" 坐标: [110,1354][186,1453]
14:48:17.792/D: 文字项19: "明星" 坐标: [351,1354][427,1453]
14:48:17.792/D: 文字项20: "帅哥" 坐标: [537,1354][613,1453]
14:48:17.793/D: 文字项21: "网文小说" 坐标: [723,1354][875,1453]
14:48:17.793/D: 文字项22: "搞笑段子" 坐标: [110,1475][262,1574]
14:48:17.793/D: 文字项23: "短剧" 坐标: [372,1475][448,1574]
14:48:17.793/D: 文字项24: "综艺" 坐标: [558,1475][634,1574]
14:48:17.794/D: 文字项25: "电视剧" 坐标: [744,1475][858,1574]
14:48:17.794/D: 文字项26: "电影" 坐标: [110,1596][186,1695]
14:48:17.794/D: 文字项27: "生活记录" 坐标: [97,1750][1014,1805]
14:48:17.794/D: 文字项28: "Vlog记录" 坐标: [110,1838][265,1937]
14:48:17.795/D: 文字项29: "萌宠" 坐标: [430,1838][506,1937]
14:48:17.795/D: 文字项30: "运动" 坐标: [97,1992][1014,2047]
14:48:17.795/D: 文字项31: "选择4个兴趣" 坐标: [44,2075][1036,2174]
14:48:17.795/D: 文字项32: "已有账号，去登录" 坐标: [0,0][0,0]
14:48:17.796/D: 匹配模式列表: ["已有账号，去登录", "选择*个兴趣"]
14:48:17.796/D: 正在检查文字 "跳过" 是否匹配模式...
14:48:17.797/D:   模式 "已有账号，去登录" 匹配结果: false
14:48:17.798/D: 🔍 通配符匹配详情: 文字="跳过", 模式="选择*个兴趣", 正则="选择.*个兴趣"
14:48:17.798/D: 🔍 正则匹配结果: false
14:48:17.798/D:   模式 "选择*个兴趣" 匹配结果: false
14:48:17.799/D: ✅ 匹配到文字: "跳过" 坐标: [949,129][1025,184] 原因: 必须关键词
14:48:17.801/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 1 个结果====🔚🔚🔚
14:48:17.801/D: 🔛🔛🔛====点击XML关键词开始====🔛🔛🔛
14:48:17.802/D: 要点击的关键词模式: "跳过"
14:48:17.802/D: ✅ 找到匹配项: "跳过" 匹配模式: "跳过" 匹配类型: 精确匹配
14:48:17.803/D: 🎯 准备点击: "跳过" 坐标: (987, 157) 偏移: (0, 0) 次数: 1 间隔: 50ms
14:48:17.803/D: su -c 'input tap 984 152.5'
14:48:17.872/D: ✅ 点击执行完成
14:48:17.873/D: 🔚🔚🔚====点击XML关键词结束====🔚🔚🔚
14:48:20.873/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:20.875/D: 参数：必须关键词="微信登录", 匹配关键词="", 匹配数量=1, 排除关键词=""
14:48:20.877/D: 获取新的XML内容
14:48:20.883/D: 开始获取界面 XML...
14:48:23.507/D: 成功获取 XML 文件，大小: 3424 字节
14:48:23.508/D: 开始解析XML获取所有文字...
14:48:23.508/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" c...
14:48:23.509/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:23.509/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:23.510/D: 找到节点: <node index="0" text="帮助" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android....
14:48:23.511/D: 从text属性提取到文字: "帮助"
14:48:23.511/D: 找到bounds: bounds="[948,154][1036,213]"
14:48:23.512/D: 添加文字项: "帮助" 坐标: [948,154][1036,213]
14:48:23.513/D: 找到节点: <node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.vi...
14:48:23.514/D: 找到bounds: bounds="[162,1531][918,1652]"
14:48:23.514/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:23.515/D: 找到节点: <node index="0" text="微信登录" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androi...
14:48:23.516/D: 从text属性提取到文字: "微信登录"
14:48:23.517/D: 找到bounds: bounds="[419,1562][661,1621]"
14:48:23.517/D: 添加文字项: "微信登录" 坐标: [419,1562][661,1621]
14:48:23.518/D: 找到节点: <node index="1" text="(上次登录)" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andr...
14:48:23.519/D: 从text属性提取到文字: "(上次登录)"
14:48:23.520/D: 找到bounds: bounds="[675,1571][813,1612]"
14:48:23.520/D: 添加文字项: "(上次登录)" 坐标: [675,1571][813,1612]
14:48:23.522/D: 找到节点: <node index="2" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.wi...
14:48:23.522/D: 找到bounds: bounds="[162,1696][918,1816]"
14:48:23.523/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:23.523/D: 找到节点: <node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.wi...
14:48:23.524/D: 从content-desc属性提取到文字: "未选中，同意"
14:48:23.525/D: 找到bounds: bounds="[162,1702][201,1741]"
14:48:23.525/D: 添加文字项: "未选中，同意" 坐标: [162,1702][201,1741]
14:48:23.526/D: 找到节点: <node index="1" text="我已阅读并同意《用户协议》《隐私政策》《未成年人个人信息保护规则》" resource-id="com.xingin.xhs:id/0_resource_n...
14:48:23.527/D: 从text属性提取到文字: "我已阅读并同意《用户协议》《隐私政策》《未成年人个人信息保护规则》"
14:48:23.527/D: 找到bounds: bounds="[212,1696][918,1816]"
14:48:23.527/D: 添加文字项: "我已阅读并同意《用户协议》《隐私政策》《未成年人个人信息保护规则》" 坐标: [212,1696][918,1816]
14:48:23.528/D: 找到节点: <node index="3" text="其他登录方式" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="andr...
14:48:23.529/D: 从text属性提取到文字: "其他登录方式"
14:48:23.529/D: 找到bounds: bounds="[422,2087][658,2132]"
14:48:23.530/D: 添加文字项: "其他登录方式" 坐标: [422,2087][658,2132]
14:48:23.531/D: XML解析成功，共提取到 6 个文字项
14:48:23.532/D: 提取到的所有文字: "帮助", "微信登录", "(上次登录)", "未选中，同意", "我已阅读并同意《用户协议》《隐私政策》《未成年人个人信息保护规则》", "其他登录方式"
14:48:23.532/D: 文字项1: "帮助" 坐标: [948,154][1036,213]
14:48:23.533/D: 文字项2: "微信登录" 坐标: [419,1562][661,1621]
14:48:23.533/D: 文字项3: "(上次登录)" 坐标: [675,1571][813,1612]
14:48:23.534/D: 文字项4: "未选中，同意" 坐标: [162,1702][201,1741]
14:48:23.534/D: 文字项5: "我已阅读并同意《用户协议》《隐私政策》《未成年人个人信息保护规则》" 坐标: [212,1696][918,1816]
14:48:23.535/D: 文字项6: "其他登录方式" 坐标: [422,2087][658,2132]
14:48:23.536/D: ✅ 匹配到文字: "微信登录" 坐标: [419,1562][661,1621] 原因: 必须关键词
14:48:23.536/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 1 个结果====🔚🔚🔚
14:48:24.537/D: 备份工具箱删除备份
14:48:25.854/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:25.855/D: 参数：必须关键词="*小红书*", 匹配关键词="*平台列表*|*今日头条*|*通道*|*APP*", 匹配数量=3, 排除关键词="*复扫*"
14:48:25.856/D: 使用缓存的XML内容
14:48:25.857/D: 开始获取界面 XML...
14:48:28.308/D: 成功获取 XML 文件，大小: 1128 字节
14:48:28.308/D: 开始解析XML获取所有文字...
14:48:28.309/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:28.310/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:28.310/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:28.310/D: 找到节点: <node index="0" text="" resource-id="com.wb.www.yytt:id/mViewPager" class="androidx.viewpager.widget...
14:48:28.311/D: 找到bounds: bounds="[0,96][1080,2174]"
14:48:28.311/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:28.312/D: 找到节点: <node index="1" text="点击跳过" resource-id="com.wb.www.yytt:id/jumpButton" class="android.widget.TextVi...
14:48:28.312/D: 从text属性提取到文字: "点击跳过"
14:48:28.313/D: 找到bounds: bounds="[892,96][1080,197]"
14:48:28.313/D: 添加文字项: "点击跳过" 坐标: [892,96][1080,197]
14:48:28.314/D: XML解析成功，共提取到 1 个文字项
14:48:28.314/D: 提取到的所有文字: "点击跳过"
14:48:28.314/D: 文字项1: "点击跳过" 坐标: [892,96][1080,197]
14:48:28.315/D: 匹配模式列表: ["*平台列表*", "*今日头条*", "*通道*", "*APP*"]
14:48:28.315/D: 🔍 检查必须关键词通配符匹配: "点击跳过" vs "*小红书*"
14:48:28.316/D: 🔍 通配符匹配详情: 文字="点击跳过", 模式="*小红书*", 正则=".*小红书.*"
14:48:28.316/D: 🔍 正则匹配结果: false
14:48:28.317/D: 🔍 通配符匹配结果: false
14:48:28.317/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:28.317/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:28.318/D: 参数：必须关键词="*小红书*", 匹配关键词="*平台列表*|*今日头条*|*通道*|*APP*", 匹配数量=3, 排除关键词="*复扫*"
14:48:28.318/D: 使用缓存的XML内容
14:48:28.318/D: 开始获取界面 XML...
14:48:30.633/D: 成功获取 XML 文件，大小: 1128 字节
14:48:30.633/D: 开始解析XML获取所有文字...
14:48:30.634/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:30.634/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:30.635/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:30.635/D: 找到节点: <node index="0" text="" resource-id="com.wb.www.yytt:id/mViewPager" class="androidx.viewpager.widget...
14:48:30.637/D: 找到bounds: bounds="[0,96][1080,2174]"
14:48:30.637/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:30.638/D: 找到节点: <node index="1" text="点击跳过" resource-id="com.wb.www.yytt:id/jumpButton" class="android.widget.TextVi...
14:48:30.642/D: 从text属性提取到文字: "点击跳过"
14:48:30.643/D: 找到bounds: bounds="[892,96][1080,197]"
14:48:30.644/D: 添加文字项: "点击跳过" 坐标: [892,96][1080,197]
14:48:30.645/D: XML解析成功，共提取到 1 个文字项
14:48:30.645/D: 提取到的所有文字: "点击跳过"
14:48:30.646/D: 文字项1: "点击跳过" 坐标: [892,96][1080,197]
14:48:30.649/D: 匹配模式列表: ["*平台列表*", "*今日头条*", "*通道*", "*APP*"]
14:48:30.650/D: 🔍 检查必须关键词通配符匹配: "点击跳过" vs "*小红书*"
14:48:30.654/D: 🔍 通配符匹配详情: 文字="点击跳过", 模式="*小红书*", 正则=".*小红书.*"
14:48:30.655/D: 🔍 正则匹配结果: false
14:48:30.656/D: 🔍 通配符匹配结果: false
14:48:30.657/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:30.660/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:30.661/D: 参数：必须关键词="*小红书*", 匹配关键词="*平台列表*|*今日头条*|*通道*|*APP*", 匹配数量=3, 排除关键词="*复扫*"
14:48:30.662/D: 使用缓存的XML内容
14:48:30.662/D: 开始获取界面 XML...
14:48:32.900/D: 成功获取 XML 文件，大小: 2531 字节
14:48:32.900/D: 开始解析XML获取所有文字...
14:48:32.901/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:32.901/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:32.901/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:32.902/D: 找到节点: <node index="0" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:32.902/D: 从text属性提取到文字: "平台列表"
14:48:32.902/D: 找到bounds: bounds="[44,123][208,179]"
14:48:32.903/D: 添加文字项: "平台列表" 坐标: [44,123][208,179]
14:48:32.904/D: 找到节点: <node index="1" text="" resource-id="com.wb.www.yytt:id/action_search" class="android.widget.TextVie...
14:48:32.904/D: 从content-desc属性提取到文字: "与梦"
14:48:32.904/D: 找到bounds: bounds="[948,96][1080,206]"
14:48:32.905/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:48:32.905/D: 找到节点: <node index="5" text="" resource-id="com.wb.www.yytt:id/mRecyclerView" class="androidx.recyclerview....
14:48:32.906/D: 找到bounds: bounds="[0,206][1080,2119]"
14:48:32.906/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:32.907/D: 找到节点: <node index="8" text="平台列表" resource-id="com.wb.www.yytt:id/idTabListText" class="android.widget.Tex...
14:48:32.907/D: 从text属性提取到文字: "平台列表"
14:48:32.908/D: 找到bounds: bounds="[102,2139][258,2174]"
14:48:32.908/D: 添加文字项: "平台列表" 坐标: [102,2139][258,2174]
14:48:32.909/D: 找到节点: <node index="9" text="授权记录" resource-id="com.wb.www.yytt:id/idTabRecodeText" class="android.widget.T...
14:48:32.909/D: 从text属性提取到文字: "授权记录"
14:48:32.909/D: 找到bounds: bounds="[462,2141][618,2174]"
14:48:32.910/D: 添加文字项: "授权记录" 坐标: [462,2141][618,2174]
14:48:32.910/D: 找到节点: <node index="10" text="我的" resource-id="com.wb.www.yytt:id/idTabMineText" class="android.widget.Text...
14:48:32.911/D: 从text属性提取到文字: "我的"
14:48:32.911/D: 找到bounds: bounds="[861,2141][939,2174]"
14:48:32.911/D: 添加文字项: "我的" 坐标: [861,2141][939,2174]
14:48:32.912/D: XML解析成功，共提取到 5 个文字项
14:48:32.912/D: 提取到的所有文字: "平台列表", "与梦", "平台列表", "授权记录", "我的"
14:48:32.913/D: 文字项1: "平台列表" 坐标: [44,123][208,179]
14:48:32.913/D: 文字项2: "与梦" 坐标: [948,96][1080,206]
14:48:32.913/D: 文字项3: "平台列表" 坐标: [102,2139][258,2174]
14:48:32.914/D: 文字项4: "授权记录" 坐标: [462,2141][618,2174]
14:48:32.914/D: 文字项5: "我的" 坐标: [861,2141][939,2174]
14:48:32.914/D: 匹配模式列表: ["*平台列表*", "*今日头条*", "*通道*", "*APP*"]
14:48:32.915/D: 🔍 检查必须关键词通配符匹配: "平台列表" vs "*小红书*"
14:48:32.916/D: 🔍 通配符匹配详情: 文字="平台列表", 模式="*小红书*", 正则=".*小红书.*"
14:48:32.916/D: 🔍 正则匹配结果: false
14:48:32.916/D: 🔍 通配符匹配结果: false
14:48:32.917/D: 🔍 检查必须关键词通配符匹配: "与梦" vs "*小红书*"
14:48:32.917/D: 🔍 通配符匹配详情: 文字="与梦", 模式="*小红书*", 正则=".*小红书.*"
14:48:32.917/D: 🔍 正则匹配结果: false
14:48:32.918/D: 🔍 通配符匹配结果: false
14:48:32.918/D: 🔍 检查必须关键词通配符匹配: "平台列表" vs "*小红书*"
14:48:32.918/D: 🔍 通配符匹配详情: 文字="平台列表", 模式="*小红书*", 正则=".*小红书.*"
14:48:32.919/D: 🔍 正则匹配结果: false
14:48:32.919/D: 🔍 通配符匹配结果: false
14:48:32.919/D: 🔍 检查必须关键词通配符匹配: "授权记录" vs "*小红书*"
14:48:32.920/D: 🔍 通配符匹配详情: 文字="授权记录", 模式="*小红书*", 正则=".*小红书.*"
14:48:32.920/D: 🔍 正则匹配结果: false
14:48:32.920/D: 🔍 通配符匹配结果: false
14:48:32.920/D: 🔍 检查必须关键词通配符匹配: "我的" vs "*小红书*"
14:48:32.921/D: 🔍 通配符匹配详情: 文字="我的", 模式="*小红书*", 正则=".*小红书.*"
14:48:32.921/D: 🔍 正则匹配结果: false
14:48:32.921/D: 🔍 通配符匹配结果: false
14:48:32.922/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:32.922/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:32.922/D: 参数：必须关键词="*小红书*", 匹配关键词="*平台列表*|*今日头条*|*通道*|*APP*", 匹配数量=3, 排除关键词="*复扫*"
14:48:32.922/D: 使用缓存的XML内容
14:48:32.922/D: 开始获取界面 XML...
14:48:35.210/D: 成功获取 XML 文件，大小: 2531 字节
14:48:35.211/D: 开始解析XML获取所有文字...
14:48:35.212/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:35.213/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:35.214/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:35.216/D: 找到节点: <node index="0" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:35.217/D: 从text属性提取到文字: "平台列表"
14:48:35.218/D: 找到bounds: bounds="[44,123][208,179]"
14:48:35.219/D: 添加文字项: "平台列表" 坐标: [44,123][208,179]
14:48:35.221/D: 找到节点: <node index="1" text="" resource-id="com.wb.www.yytt:id/action_search" class="android.widget.TextVie...
14:48:35.222/D: 从content-desc属性提取到文字: "与梦"
14:48:35.223/D: 找到bounds: bounds="[948,96][1080,206]"
14:48:35.224/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:48:35.226/D: 找到节点: <node index="5" text="" resource-id="com.wb.www.yytt:id/mRecyclerView" class="androidx.recyclerview....
14:48:35.228/D: 找到bounds: bounds="[0,206][1080,2119]"
14:48:35.229/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:35.230/D: 找到节点: <node index="8" text="平台列表" resource-id="com.wb.www.yytt:id/idTabListText" class="android.widget.Tex...
14:48:35.231/D: 从text属性提取到文字: "平台列表"
14:48:35.232/D: 找到bounds: bounds="[102,2139][258,2174]"
14:48:35.233/D: 添加文字项: "平台列表" 坐标: [102,2139][258,2174]
14:48:35.234/D: 找到节点: <node index="9" text="授权记录" resource-id="com.wb.www.yytt:id/idTabRecodeText" class="android.widget.T...
14:48:35.236/D: 从text属性提取到文字: "授权记录"
14:48:35.236/D: 找到bounds: bounds="[462,2141][618,2174]"
14:48:35.237/D: 添加文字项: "授权记录" 坐标: [462,2141][618,2174]
14:48:35.239/D: 找到节点: <node index="10" text="我的" resource-id="com.wb.www.yytt:id/idTabMineText" class="android.widget.Text...
14:48:35.241/D: 从text属性提取到文字: "我的"
14:48:35.241/D: 找到bounds: bounds="[861,2141][939,2174]"
14:48:35.242/D: 添加文字项: "我的" 坐标: [861,2141][939,2174]
14:48:35.244/D: XML解析成功，共提取到 5 个文字项
14:48:35.245/D: 提取到的所有文字: "平台列表", "与梦", "平台列表", "授权记录", "我的"
14:48:35.246/D: 文字项1: "平台列表" 坐标: [44,123][208,179]
14:48:35.247/D: 文字项2: "与梦" 坐标: [948,96][1080,206]
14:48:35.248/D: 文字项3: "平台列表" 坐标: [102,2139][258,2174]
14:48:35.249/D: 文字项4: "授权记录" 坐标: [462,2141][618,2174]
14:48:35.250/D: 文字项5: "我的" 坐标: [861,2141][939,2174]
14:48:35.251/D: 匹配模式列表: ["*平台列表*", "*今日头条*", "*通道*", "*APP*"]
14:48:35.253/D: 🔍 检查必须关键词通配符匹配: "平台列表" vs "*小红书*"
14:48:35.255/D: 🔍 通配符匹配详情: 文字="平台列表", 模式="*小红书*", 正则=".*小红书.*"
14:48:35.256/D: 🔍 正则匹配结果: false
14:48:35.256/D: 🔍 通配符匹配结果: false
14:48:35.257/D: 🔍 检查必须关键词通配符匹配: "与梦" vs "*小红书*"
14:48:35.258/D: 🔍 通配符匹配详情: 文字="与梦", 模式="*小红书*", 正则=".*小红书.*"
14:48:35.259/D: 🔍 正则匹配结果: false
14:48:35.260/D: 🔍 通配符匹配结果: false
14:48:35.261/D: 🔍 检查必须关键词通配符匹配: "平台列表" vs "*小红书*"
14:48:35.263/D: 🔍 通配符匹配详情: 文字="平台列表", 模式="*小红书*", 正则=".*小红书.*"
14:48:35.263/D: 🔍 正则匹配结果: false
14:48:35.264/D: 🔍 通配符匹配结果: false
14:48:35.264/D: 🔍 检查必须关键词通配符匹配: "授权记录" vs "*小红书*"
14:48:35.265/D: 🔍 通配符匹配详情: 文字="授权记录", 模式="*小红书*", 正则=".*小红书.*"
14:48:35.265/D: 🔍 正则匹配结果: false
14:48:35.265/D: 🔍 通配符匹配结果: false
14:48:35.266/D: 🔍 检查必须关键词通配符匹配: "我的" vs "*小红书*"
14:48:35.266/D: 🔍 通配符匹配详情: 文字="我的", 模式="*小红书*", 正则=".*小红书.*"
14:48:35.267/D: 🔍 正则匹配结果: false
14:48:35.267/D: 🔍 通配符匹配结果: false
14:48:35.267/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:35.267/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:35.268/D: 参数：必须关键词="*小红书*", 匹配关键词="*平台列表*|*今日头条*|*通道*|*APP*", 匹配数量=3, 排除关键词="*复扫*"
14:48:35.268/D: 使用缓存的XML内容
14:48:35.268/D: 开始获取界面 XML...
14:48:37.658/D: 成功获取 XML 文件，大小: 17410 字节
14:48:37.658/D: 开始解析XML获取所有文字...
14:48:37.659/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:37.660/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:37.660/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.661/D: 找到节点: <node index="0" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:37.661/D: 从text属性提取到文字: "平台列表"
14:48:37.661/D: 找到bounds: bounds="[44,123][208,179]"
14:48:37.662/D: 添加文字项: "平台列表" 坐标: [44,123][208,179]
14:48:37.663/D: 找到节点: <node index="1" text="" resource-id="com.wb.www.yytt:id/action_search" class="android.widget.TextVie...
14:48:37.663/D: 从content-desc属性提取到文字: "与梦"
14:48:37.664/D: 找到bounds: bounds="[948,96][1080,206]"
14:48:37.664/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:48:37.665/D: 找到节点: <node index="5" text="" resource-id="com.wb.www.yytt:id/mRecyclerView" class="androidx.recyclerview....
14:48:37.665/D: 找到bounds: bounds="[0,206][1080,2119]"
14:48:37.665/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.666/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.wb.www.yytt" cont...
14:48:37.666/D: 找到bounds: bounds="[0,206][1080,481]"
14:48:37.667/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.667/D: 找到节点: <node index="0" text="今日头条《APP》" resource-id="com.wb.www.yytt:id/mItemTitle" class="android.widget.T...
14:48:37.667/D: 从text属性提取到文字: "今日头条《APP》"
14:48:37.668/D: 找到bounds: bounds="[262,275][570,327]"
14:48:37.668/D: 添加文字项: "今日头条《APP》" 坐标: [262,275][570,327]
14:48:37.669/D: 找到节点: <node index="1" text="通道一:1    " resource-id="com.wb.www.yytt:id/priceText1" class="android.widget.T...
14:48:37.669/D: 从text属性提取到文字: "通道一:1"
14:48:37.670/D: 找到bounds: bounds="[262,404][407,445]"
14:48:37.670/D: 添加文字项: "通道一:1" 坐标: [262,404][407,445]
14:48:37.671/D: 找到节点: <node index="2" text="通道二:1.2    " resource-id="com.wb.www.yytt:id/priceText2" class="android.widget...
14:48:37.671/D: 从text属性提取到文字: "通道二:1.2"
14:48:37.672/D: 找到bounds: bounds="[407,404][577,445]"
14:48:37.672/D: 添加文字项: "通道二:1.2" 坐标: [407,404][577,445]
14:48:37.673/D: 找到节点: <node index="3" text="通道三:1.5    " resource-id="com.wb.www.yytt:id/priceText3" class="android.widget...
14:48:37.673/D: 从text属性提取到文字: "通道三:1.5"
14:48:37.673/D: 找到bounds: bounds="[577,404][748,445]"
14:48:37.674/D: 添加文字项: "通道三:1.5" 坐标: [577,404][748,445]
14:48:37.674/D: 找到节点: <node NAF="true" index="4" text="" resource-id="com.wb.www.yytt:id/mItemSaoMa" class="android.widget...
14:48:37.675/D: 找到bounds: bounds="[914,280][1052,418]"
14:48:37.675/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.676/D: 找到节点: <node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.wb.www.yytt" cont...
14:48:37.676/D: 找到bounds: bounds="[0,481][1080,756]"
14:48:37.676/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.677/D: 找到节点: <node index="0" text="拼多多《APP》" resource-id="com.wb.www.yytt:id/mItemTitle" class="android.widget.Te...
14:48:37.677/D: 从text属性提取到文字: "拼多多《APP》"
14:48:37.677/D: 找到bounds: bounds="[262,550][531,602]"
14:48:37.678/D: 添加文字项: "拼多多《APP》" 坐标: [262,550][531,602]
14:48:37.678/D: 找到节点: <node index="1" text="通道一:1.5    " resource-id="com.wb.www.yytt:id/priceText1" class="android.widget...
14:48:37.679/D: 从text属性提取到文字: "通道一:1.5"
14:48:37.679/D: 找到bounds: bounds="[262,679][433,720]"
14:48:37.680/D: 添加文字项: "通道一:1.5" 坐标: [262,679][433,720]
14:48:37.680/D: 找到节点: <node index="2" text="通道二:1.2    " resource-id="com.wb.www.yytt:id/priceText2" class="android.widget...
14:48:37.681/D: 从text属性提取到文字: "通道二:1.2"
14:48:37.681/D: 找到bounds: bounds="[433,679][603,720]"
14:48:37.681/D: 添加文字项: "通道二:1.2" 坐标: [433,679][603,720]
14:48:37.682/D: 找到节点: <node index="3" text="通道三:3    " resource-id="com.wb.www.yytt:id/priceText3" class="android.widget.T...
14:48:37.682/D: 从text属性提取到文字: "通道三:3"
14:48:37.683/D: 找到bounds: bounds="[603,679][754,720]"
14:48:37.683/D: 添加文字项: "通道三:3" 坐标: [603,679][754,720]
14:48:37.684/D: 找到节点: <node NAF="true" index="4" text="" resource-id="com.wb.www.yytt:id/mItemSaoMa" class="android.widget...
14:48:37.684/D: 找到bounds: bounds="[914,555][1052,693]"
14:48:37.685/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.685/D: 找到节点: <node index="2" text="" resource-id="" class="android.view.ViewGroup" package="com.wb.www.yytt" cont...
14:48:37.686/D: 找到bounds: bounds="[0,756][1080,1031]"
14:48:37.686/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.686/D: 找到节点: <node index="0" text="快手《APP》" resource-id="com.wb.www.yytt:id/mItemTitle" class="android.widget.Tex...
14:48:37.687/D: 从text属性提取到文字: "快手《APP》"
14:48:37.687/D: 找到bounds: bounds="[262,825][492,877]"
14:48:37.687/D: 添加文字项: "快手《APP》" 坐标: [262,825][492,877]
14:48:37.688/D: 找到节点: <node index="1" text="通道一:2.5    " resource-id="com.wb.www.yytt:id/priceText1" class="android.widget...
14:48:37.689/D: 从text属性提取到文字: "通道一:2.5"
14:48:37.689/D: 找到bounds: bounds="[262,954][438,995]"
14:48:37.689/D: 添加文字项: "通道一:2.5" 坐标: [262,954][438,995]
14:48:37.690/D: 找到节点: <node index="2" text="通道二:2.5    " resource-id="com.wb.www.yytt:id/priceText2" class="android.widget...
14:48:37.691/D: 从text属性提取到文字: "通道二:2.5"
14:48:37.691/D: 找到bounds: bounds="[438,954][614,995]"
14:48:37.691/D: 添加文字项: "通道二:2.5" 坐标: [438,954][614,995]
14:48:37.692/D: 找到节点: <node index="3" text="通道三:2.5    " resource-id="com.wb.www.yytt:id/priceText3" class="android.widget...
14:48:37.693/D: 从text属性提取到文字: "通道三:2.5"
14:48:37.693/D: 找到bounds: bounds="[614,954][790,995]"
14:48:37.693/D: 添加文字项: "通道三:2.5" 坐标: [614,954][790,995]
14:48:37.694/D: 找到节点: <node NAF="true" index="4" text="" resource-id="com.wb.www.yytt:id/mItemSaoMa" class="android.widget...
14:48:37.694/D: 找到bounds: bounds="[914,830][1052,968]"
14:48:37.695/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.695/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.ViewGroup" package="com.wb.www.yytt" cont...
14:48:37.696/D: 找到bounds: bounds="[0,1031][1080,1306]"
14:48:37.696/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.696/D: 找到节点: <node index="0" text="百度极速《APP》" resource-id="com.wb.www.yytt:id/mItemTitle" class="android.widget.T...
14:48:37.697/D: 从text属性提取到文字: "百度极速《APP》"
14:48:37.697/D: 找到bounds: bounds="[262,1100][570,1152]"
14:48:37.697/D: 添加文字项: "百度极速《APP》" 坐标: [262,1100][570,1152]
14:48:37.698/D: 找到节点: <node index="1" text="通道一:1.2    " resource-id="com.wb.www.yytt:id/priceText1" class="android.widget...
14:48:37.699/D: 从text属性提取到文字: "通道一:1.2"
14:48:37.699/D: 找到bounds: bounds="[262,1229][432,1270]"
14:48:37.699/D: 添加文字项: "通道一:1.2" 坐标: [262,1229][432,1270]
14:48:37.700/D: 找到节点: <node index="2" text="通道二:1.2    " resource-id="com.wb.www.yytt:id/priceText2" class="android.widget...
14:48:37.700/D: 从text属性提取到文字: "通道二:1.2"
14:48:37.701/D: 找到bounds: bounds="[432,1229][602,1270]"
14:48:37.701/D: 添加文字项: "通道二:1.2" 坐标: [432,1229][602,1270]
14:48:37.702/D: 找到节点: <node index="3" text="通道三:2    " resource-id="com.wb.www.yytt:id/priceText3" class="android.widget.T...
14:48:37.702/D: 从text属性提取到文字: "通道三:2"
14:48:37.702/D: 找到bounds: bounds="[602,1229][752,1270]"
14:48:37.703/D: 添加文字项: "通道三:2" 坐标: [602,1229][752,1270]
14:48:37.703/D: 找到节点: <node NAF="true" index="4" text="" resource-id="com.wb.www.yytt:id/mItemSaoMa" class="android.widget...
14:48:37.704/D: 找到bounds: bounds="[914,1105][1052,1243]"
14:48:37.704/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.705/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.ViewGroup" package="com.wb.www.yytt" cont...
14:48:37.705/D: 找到bounds: bounds="[0,1306][1080,1581]"
14:48:37.706/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.706/D: 找到节点: <node index="0" text="京东《APP》" resource-id="com.wb.www.yytt:id/mItemTitle" class="android.widget.Tex...
14:48:37.706/D: 从text属性提取到文字: "京东《APP》"
14:48:37.706/D: 找到bounds: bounds="[262,1375][492,1427]"
14:48:37.707/D: 添加文字项: "京东《APP》" 坐标: [262,1375][492,1427]
14:48:37.708/D: 找到节点: <node index="1" text="通道一:1    " resource-id="com.wb.www.yytt:id/priceText1" class="android.widget.T...
14:48:37.708/D: 从text属性提取到文字: "通道一:1"
14:48:37.709/D: 找到bounds: bounds="[262,1504][407,1545]"
14:48:37.709/D: 添加文字项: "通道一:1" 坐标: [262,1504][407,1545]
14:48:37.710/D: 找到节点: <node index="2" text="通道二:1    " resource-id="com.wb.www.yytt:id/priceText2" class="android.widget.T...
14:48:37.711/D: 从text属性提取到文字: "通道二:1"
14:48:37.711/D: 找到bounds: bounds="[407,1504][552,1545]"
14:48:37.711/D: 添加文字项: "通道二:1" 坐标: [407,1504][552,1545]
14:48:37.712/D: 找到节点: <node index="3" text="通道三:1    " resource-id="com.wb.www.yytt:id/priceText3" class="android.widget.T...
14:48:37.713/D: 从text属性提取到文字: "通道三:1"
14:48:37.713/D: 找到bounds: bounds="[552,1504][697,1545]"
14:48:37.713/D: 添加文字项: "通道三:1" 坐标: [552,1504][697,1545]
14:48:37.714/D: 找到节点: <node NAF="true" index="4" text="" resource-id="com.wb.www.yytt:id/mItemSaoMa" class="android.widget...
14:48:37.715/D: 找到bounds: bounds="[914,1380][1052,1518]"
14:48:37.715/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.715/D: 找到节点: <node index="5" text="" resource-id="" class="android.view.ViewGroup" package="com.wb.www.yytt" cont...
14:48:37.716/D: 找到bounds: bounds="[0,1581][1080,1856]"
14:48:37.716/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.716/D: 找到节点: <node index="0" text="小红书《APP》" resource-id="com.wb.www.yytt:id/mItemTitle" class="android.widget.Te...
14:48:37.717/D: 从text属性提取到文字: "小红书《APP》"
14:48:37.717/D: 找到bounds: bounds="[262,1650][531,1702]"
14:48:37.717/D: 添加文字项: "小红书《APP》" 坐标: [262,1650][531,1702]
14:48:37.718/D: 找到节点: <node index="1" text="通道一:1.2    " resource-id="com.wb.www.yytt:id/priceText1" class="android.widget...
14:48:37.718/D: 从text属性提取到文字: "通道一:1.2"
14:48:37.719/D: 找到bounds: bounds="[262,1779][432,1820]"
14:48:37.719/D: 添加文字项: "通道一:1.2" 坐标: [262,1779][432,1820]
14:48:37.720/D: 找到节点: <node index="2" text="通道二:0.8    " resource-id="com.wb.www.yytt:id/priceText2" class="android.widget...
14:48:37.720/D: 从text属性提取到文字: "通道二:0.8"
14:48:37.720/D: 找到bounds: bounds="[432,1779][611,1820]"
14:48:37.721/D: 添加文字项: "通道二:0.8" 坐标: [432,1779][611,1820]
14:48:37.721/D: 找到节点: <node index="3" text="通道三:3    " resource-id="com.wb.www.yytt:id/priceText3" class="android.widget.T...
14:48:37.722/D: 从text属性提取到文字: "通道三:3"
14:48:37.722/D: 找到bounds: bounds="[611,1779][762,1820]"
14:48:37.722/D: 添加文字项: "通道三:3" 坐标: [611,1779][762,1820]
14:48:37.723/D: 找到节点: <node NAF="true" index="4" text="" resource-id="com.wb.www.yytt:id/mItemSaoMa" class="android.widget...
14:48:37.724/D: 找到bounds: bounds="[914,1655][1052,1793]"
14:48:37.724/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.725/D: 找到节点: <node index="6" text="" resource-id="" class="android.view.ViewGroup" package="com.wb.www.yytt" cont...
14:48:37.725/D: 找到bounds: bounds="[0,1856][1080,2119]"
14:48:37.725/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.726/D: 找到节点: <node index="0" text="懂车帝《APP》" resource-id="com.wb.www.yytt:id/mItemTitle" class="android.widget.Te...
14:48:37.726/D: 从text属性提取到文字: "懂车帝《APP》"
14:48:37.726/D: 找到bounds: bounds="[262,1925][531,1977]"
14:48:37.727/D: 添加文字项: "懂车帝《APP》" 坐标: [262,1925][531,1977]
14:48:37.728/D: 找到节点: <node index="1" text="通道一:0.8    " resource-id="com.wb.www.yytt:id/priceText1" class="android.widget...
14:48:37.728/D: 从text属性提取到文字: "通道一:0.8"
14:48:37.728/D: 找到bounds: bounds="[262,2054][441,2095]"
14:48:37.729/D: 添加文字项: "通道一:0.8" 坐标: [262,2054][441,2095]
14:48:37.730/D: 找到节点: <node index="2" text="通道二:1.5    " resource-id="com.wb.www.yytt:id/priceText2" class="android.widget...
14:48:37.731/D: 从text属性提取到文字: "通道二:1.5"
14:48:37.731/D: 找到bounds: bounds="[441,2054][612,2095]"
14:48:37.732/D: 添加文字项: "通道二:1.5" 坐标: [441,2054][612,2095]
14:48:37.733/D: 找到节点: <node index="3" text="通道三:1.5    " resource-id="com.wb.www.yytt:id/priceText3" class="android.widget...
14:48:37.733/D: 从text属性提取到文字: "通道三:1.5"
14:48:37.734/D: 找到bounds: bounds="[612,2054][783,2095]"
14:48:37.734/D: 添加文字项: "通道三:1.5" 坐标: [612,2054][783,2095]
14:48:37.735/D: 找到节点: <node NAF="true" index="4" text="" resource-id="com.wb.www.yytt:id/mItemSaoMa" class="android.widget...
14:48:37.736/D: 找到bounds: bounds="[914,1930][1052,2068]"
14:48:37.736/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:37.736/D: 找到节点: <node index="8" text="平台列表" resource-id="com.wb.www.yytt:id/idTabListText" class="android.widget.Tex...
14:48:37.737/D: 从text属性提取到文字: "平台列表"
14:48:37.737/D: 找到bounds: bounds="[102,2139][258,2174]"
14:48:37.738/D: 添加文字项: "平台列表" 坐标: [102,2139][258,2174]
14:48:37.739/D: 找到节点: <node index="9" text="授权记录" resource-id="com.wb.www.yytt:id/idTabRecodeText" class="android.widget.T...
14:48:37.740/D: 从text属性提取到文字: "授权记录"
14:48:37.740/D: 找到bounds: bounds="[462,2141][618,2174]"
14:48:37.741/D: 添加文字项: "授权记录" 坐标: [462,2141][618,2174]
14:48:37.741/D: 找到节点: <node index="10" text="我的" resource-id="com.wb.www.yytt:id/idTabMineText" class="android.widget.Text...
14:48:37.742/D: 从text属性提取到文字: "我的"
14:48:37.742/D: 找到bounds: bounds="[861,2141][939,2174]"
14:48:37.743/D: 添加文字项: "我的" 坐标: [861,2141][939,2174]
14:48:37.744/D: XML解析成功，共提取到 33 个文字项
14:48:37.745/D: 提取到的所有文字: "平台列表", "与梦", "今日头条《APP》", "通道一:1", "通道二:1.2", "通道三:1.5", "拼多多《APP》", "通道一:1.5", "通道二:1.2", "通道三:3", "快手《APP》", "通道一:2.5", "通道二:2.5", "通道三:2.5", "百度极速《APP》", "通道一:1.2", "通道二:1.2", "通道三:2", "京东《APP》", "通道一:1", "通道二:1", "通道三:1", "小红书《APP》", "通道一:1.2", "通道二:0.8", "通道三:3", "懂车帝《APP》", "通道一:0.8", "通道二:1.5", "通道三:1.5", "平台列表", "授权记录", "我的"
14:48:37.745/D: 文字项1: "平台列表" 坐标: [44,123][208,179]
14:48:37.746/D: 文字项2: "与梦" 坐标: [948,96][1080,206]
14:48:37.746/D: 文字项3: "今日头条《APP》" 坐标: [262,275][570,327]
14:48:37.747/D: 文字项4: "通道一:1" 坐标: [262,404][407,445]
14:48:37.747/D: 文字项5: "通道二:1.2" 坐标: [407,404][577,445]
14:48:37.748/D: 文字项6: "通道三:1.5" 坐标: [577,404][748,445]
14:48:37.748/D: 文字项7: "拼多多《APP》" 坐标: [262,550][531,602]
14:48:37.749/D: 文字项8: "通道一:1.5" 坐标: [262,679][433,720]
14:48:37.749/D: 文字项9: "通道二:1.2" 坐标: [433,679][603,720]
14:48:37.750/D: 文字项10: "通道三:3" 坐标: [603,679][754,720]
14:48:37.750/D: 文字项11: "快手《APP》" 坐标: [262,825][492,877]
14:48:37.750/D: 文字项12: "通道一:2.5" 坐标: [262,954][438,995]
14:48:37.751/D: 文字项13: "通道二:2.5" 坐标: [438,954][614,995]
14:48:37.751/D: 文字项14: "通道三:2.5" 坐标: [614,954][790,995]
14:48:37.752/D: 文字项15: "百度极速《APP》" 坐标: [262,1100][570,1152]
14:48:37.752/D: 文字项16: "通道一:1.2" 坐标: [262,1229][432,1270]
14:48:37.752/D: 文字项17: "通道二:1.2" 坐标: [432,1229][602,1270]
14:48:37.753/D: 文字项18: "通道三:2" 坐标: [602,1229][752,1270]
14:48:37.753/D: 文字项19: "京东《APP》" 坐标: [262,1375][492,1427]
14:48:37.753/D: 文字项20: "通道一:1" 坐标: [262,1504][407,1545]
14:48:37.754/D: 文字项21: "通道二:1" 坐标: [407,1504][552,1545]
14:48:37.754/D: 文字项22: "通道三:1" 坐标: [552,1504][697,1545]
14:48:37.754/D: 文字项23: "小红书《APP》" 坐标: [262,1650][531,1702]
14:48:37.755/D: 文字项24: "通道一:1.2" 坐标: [262,1779][432,1820]
14:48:37.755/D: 文字项25: "通道二:0.8" 坐标: [432,1779][611,1820]
14:48:37.755/D: 文字项26: "通道三:3" 坐标: [611,1779][762,1820]
14:48:37.756/D: 文字项27: "懂车帝《APP》" 坐标: [262,1925][531,1977]
14:48:37.756/D: 文字项28: "通道一:0.8" 坐标: [262,2054][441,2095]
14:48:37.756/D: 文字项29: "通道二:1.5" 坐标: [441,2054][612,2095]
14:48:37.757/D: 文字项30: "通道三:1.5" 坐标: [612,2054][783,2095]
14:48:37.757/D: 文字项31: "平台列表" 坐标: [102,2139][258,2174]
14:48:37.757/D: 文字项32: "授权记录" 坐标: [462,2141][618,2174]
14:48:37.757/D: 文字项33: "我的" 坐标: [861,2141][939,2174]
14:48:37.758/D: 匹配模式列表: ["*平台列表*", "*今日头条*", "*通道*", "*APP*"]
14:48:37.759/D: 🔍 检查必须关键词通配符匹配: "平台列表" vs "*小红书*"
14:48:37.759/D: 🔍 通配符匹配详情: 文字="平台列表", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.760/D: 🔍 正则匹配结果: false
14:48:37.760/D: 🔍 通配符匹配结果: false
14:48:37.760/D: 🔍 检查必须关键词通配符匹配: "与梦" vs "*小红书*"
14:48:37.761/D: 🔍 通配符匹配详情: 文字="与梦", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.761/D: 🔍 正则匹配结果: false
14:48:37.762/D: 🔍 通配符匹配结果: false
14:48:37.762/D: 🔍 检查必须关键词通配符匹配: "今日头条《APP》" vs "*小红书*"
14:48:37.762/D: 🔍 通配符匹配详情: 文字="今日头条《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.763/D: 🔍 正则匹配结果: false
14:48:37.763/D: 🔍 通配符匹配结果: false
14:48:37.764/D: 🔍 检查必须关键词通配符匹配: "通道一:1" vs "*小红书*"
14:48:37.764/D: 🔍 通配符匹配详情: 文字="通道一:1", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.765/D: 🔍 正则匹配结果: false
14:48:37.765/D: 🔍 通配符匹配结果: false
14:48:37.765/D: 🔍 检查必须关键词通配符匹配: "通道二:1.2" vs "*小红书*"
14:48:37.766/D: 🔍 通配符匹配详情: 文字="通道二:1.2", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.766/D: 🔍 正则匹配结果: false
14:48:37.766/D: 🔍 通配符匹配结果: false
14:48:37.767/D: 🔍 检查必须关键词通配符匹配: "通道三:1.5" vs "*小红书*"
14:48:37.767/D: 🔍 通配符匹配详情: 文字="通道三:1.5", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.768/D: 🔍 正则匹配结果: false
14:48:37.768/D: 🔍 通配符匹配结果: false
14:48:37.768/D: 🔍 检查必须关键词通配符匹配: "拼多多《APP》" vs "*小红书*"
14:48:37.769/D: 🔍 通配符匹配详情: 文字="拼多多《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.769/D: 🔍 正则匹配结果: false
14:48:37.769/D: 🔍 通配符匹配结果: false
14:48:37.770/D: 🔍 检查必须关键词通配符匹配: "通道一:1.5" vs "*小红书*"
14:48:37.770/D: 🔍 通配符匹配详情: 文字="通道一:1.5", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.771/D: 🔍 正则匹配结果: false
14:48:37.771/D: 🔍 通配符匹配结果: false
14:48:37.771/D: 🔍 检查必须关键词通配符匹配: "通道二:1.2" vs "*小红书*"
14:48:37.772/D: 🔍 通配符匹配详情: 文字="通道二:1.2", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.772/D: 🔍 正则匹配结果: false
14:48:37.772/D: 🔍 通配符匹配结果: false
14:48:37.773/D: 🔍 检查必须关键词通配符匹配: "通道三:3" vs "*小红书*"
14:48:37.773/D: 🔍 通配符匹配详情: 文字="通道三:3", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.774/D: 🔍 正则匹配结果: false
14:48:37.774/D: 🔍 通配符匹配结果: false
14:48:37.774/D: 🔍 检查必须关键词通配符匹配: "快手《APP》" vs "*小红书*"
14:48:37.775/D: 🔍 通配符匹配详情: 文字="快手《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.775/D: 🔍 正则匹配结果: false
14:48:37.776/D: 🔍 通配符匹配结果: false
14:48:37.776/D: 🔍 检查必须关键词通配符匹配: "通道一:2.5" vs "*小红书*"
14:48:37.777/D: 🔍 通配符匹配详情: 文字="通道一:2.5", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.777/D: 🔍 正则匹配结果: false
14:48:37.777/D: 🔍 通配符匹配结果: false
14:48:37.778/D: 🔍 检查必须关键词通配符匹配: "通道二:2.5" vs "*小红书*"
14:48:37.778/D: 🔍 通配符匹配详情: 文字="通道二:2.5", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.779/D: 🔍 正则匹配结果: false
14:48:37.779/D: 🔍 通配符匹配结果: false
14:48:37.779/D: 🔍 检查必须关键词通配符匹配: "通道三:2.5" vs "*小红书*"
14:48:37.780/D: 🔍 通配符匹配详情: 文字="通道三:2.5", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.780/D: 🔍 正则匹配结果: false
14:48:37.780/D: 🔍 通配符匹配结果: false
14:48:37.781/D: 🔍 检查必须关键词通配符匹配: "百度极速《APP》" vs "*小红书*"
14:48:37.781/D: 🔍 通配符匹配详情: 文字="百度极速《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.781/D: 🔍 正则匹配结果: false
14:48:37.782/D: 🔍 通配符匹配结果: false
14:48:37.782/D: 🔍 检查必须关键词通配符匹配: "通道一:1.2" vs "*小红书*"
14:48:37.782/D: 🔍 通配符匹配详情: 文字="通道一:1.2", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.783/D: 🔍 正则匹配结果: false
14:48:37.783/D: 🔍 通配符匹配结果: false
14:48:37.783/D: 🔍 检查必须关键词通配符匹配: "通道二:1.2" vs "*小红书*"
14:48:37.784/D: 🔍 通配符匹配详情: 文字="通道二:1.2", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.784/D: 🔍 正则匹配结果: false
14:48:37.784/D: 🔍 通配符匹配结果: false
14:48:37.784/D: 🔍 检查必须关键词通配符匹配: "通道三:2" vs "*小红书*"
14:48:37.785/D: 🔍 通配符匹配详情: 文字="通道三:2", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.785/D: 🔍 正则匹配结果: false
14:48:37.785/D: 🔍 通配符匹配结果: false
14:48:37.786/D: 🔍 检查必须关键词通配符匹配: "京东《APP》" vs "*小红书*"
14:48:37.786/D: 🔍 通配符匹配详情: 文字="京东《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.786/D: 🔍 正则匹配结果: false
14:48:37.787/D: 🔍 通配符匹配结果: false
14:48:37.787/D: 🔍 检查必须关键词通配符匹配: "通道一:1" vs "*小红书*"
14:48:37.787/D: 🔍 通配符匹配详情: 文字="通道一:1", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.788/D: 🔍 正则匹配结果: false
14:48:37.788/D: 🔍 通配符匹配结果: false
14:48:37.788/D: 🔍 检查必须关键词通配符匹配: "通道二:1" vs "*小红书*"
14:48:37.789/D: 🔍 通配符匹配详情: 文字="通道二:1", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.789/D: 🔍 正则匹配结果: false
14:48:37.789/D: 🔍 通配符匹配结果: false
14:48:37.789/D: 🔍 检查必须关键词通配符匹配: "通道三:1" vs "*小红书*"
14:48:37.790/D: 🔍 通配符匹配详情: 文字="通道三:1", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.790/D: 🔍 正则匹配结果: false
14:48:37.790/D: 🔍 通配符匹配结果: false
14:48:37.791/D: 🔍 检查必须关键词通配符匹配: "小红书《APP》" vs "*小红书*"
14:48:37.791/D: 🔍 通配符匹配详情: 文字="小红书《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.791/D: 🔍 正则匹配结果: true
14:48:37.792/D: 🔍 通配符匹配结果: true
14:48:37.792/D: 正在检查文字 "小红书《APP》" 是否匹配模式...
14:48:37.792/D: 🔍 通配符匹配详情: 文字="小红书《APP》", 模式="*平台列表*", 正则=".*平台列表.*"
14:48:37.793/D: 🔍 正则匹配结果: false
14:48:37.793/D:   模式 "*平台列表*" 匹配结果: false
14:48:37.794/D: 🔍 通配符匹配详情: 文字="小红书《APP》", 模式="*今日头条*", 正则=".*今日头条.*"
14:48:37.794/D: 🔍 正则匹配结果: false
14:48:37.795/D:   模式 "*今日头条*" 匹配结果: false
14:48:37.795/D: 🔍 通配符匹配详情: 文字="小红书《APP》", 模式="*通道*", 正则=".*通道.*"
14:48:37.796/D: 🔍 正则匹配结果: false
14:48:37.796/D:   模式 "*通道*" 匹配结果: false
14:48:37.796/D: 🔍 通配符匹配详情: 文字="小红书《APP》", 模式="*APP*", 正则=".*APP.*"
14:48:37.797/D: 🔍 正则匹配结果: true
14:48:37.797/D:   模式 "*APP*" 匹配结果: true
14:48:37.798/D: 🔍 通配符匹配详情: 文字="小红书《APP》", 模式="*复扫*", 正则=".*复扫.*"
14:48:37.798/D: 🔍 正则匹配结果: false
14:48:37.799/D: ✅ 匹配到文字: "小红书《APP》" 坐标: [262,1650][531,1702] 原因: 必须关键词 + 匹配关键词
14:48:37.799/D: 🔍 检查必须关键词通配符匹配: "通道一:1.2" vs "*小红书*"
14:48:37.800/D: 🔍 通配符匹配详情: 文字="通道一:1.2", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.800/D: 🔍 正则匹配结果: false
14:48:37.801/D: 🔍 通配符匹配结果: false
14:48:37.801/D: 🔍 检查必须关键词通配符匹配: "通道二:0.8" vs "*小红书*"
14:48:37.801/D: 🔍 通配符匹配详情: 文字="通道二:0.8", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.802/D: 🔍 正则匹配结果: false
14:48:37.802/D: 🔍 通配符匹配结果: false
14:48:37.802/D: 🔍 检查必须关键词通配符匹配: "通道三:3" vs "*小红书*"
14:48:37.803/D: 🔍 通配符匹配详情: 文字="通道三:3", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.803/D: 🔍 正则匹配结果: false
14:48:37.804/D: 🔍 通配符匹配结果: false
14:48:37.804/D: 🔍 检查必须关键词通配符匹配: "懂车帝《APP》" vs "*小红书*"
14:48:37.805/D: 🔍 通配符匹配详情: 文字="懂车帝《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.805/D: 🔍 正则匹配结果: false
14:48:37.805/D: 🔍 通配符匹配结果: false
14:48:37.806/D: 🔍 检查必须关键词通配符匹配: "通道一:0.8" vs "*小红书*"
14:48:37.806/D: 🔍 通配符匹配详情: 文字="通道一:0.8", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.807/D: 🔍 正则匹配结果: false
14:48:37.807/D: 🔍 通配符匹配结果: false
14:48:37.807/D: 🔍 检查必须关键词通配符匹配: "通道二:1.5" vs "*小红书*"
14:48:37.808/D: 🔍 通配符匹配详情: 文字="通道二:1.5", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.808/D: 🔍 正则匹配结果: false
14:48:37.808/D: 🔍 通配符匹配结果: false
14:48:37.809/D: 🔍 检查必须关键词通配符匹配: "通道三:1.5" vs "*小红书*"
14:48:37.809/D: 🔍 通配符匹配详情: 文字="通道三:1.5", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.810/D: 🔍 正则匹配结果: false
14:48:37.810/D: 🔍 通配符匹配结果: false
14:48:37.810/D: 🔍 检查必须关键词通配符匹配: "平台列表" vs "*小红书*"
14:48:37.810/D: 🔍 通配符匹配详情: 文字="平台列表", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.811/D: 🔍 正则匹配结果: false
14:48:37.811/D: 🔍 通配符匹配结果: false
14:48:37.811/D: 🔍 检查必须关键词通配符匹配: "授权记录" vs "*小红书*"
14:48:37.812/D: 🔍 通配符匹配详情: 文字="授权记录", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.812/D: 🔍 正则匹配结果: false
14:48:37.812/D: 🔍 通配符匹配结果: false
14:48:37.813/D: 🔍 检查必须关键词通配符匹配: "我的" vs "*小红书*"
14:48:37.813/D: 🔍 通配符匹配详情: 文字="我的", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.813/D: 🔍 正则匹配结果: false
14:48:37.813/D: 🔍 通配符匹配结果: false
14:48:37.814/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 1 个结果====🔚🔚🔚
14:48:37.814/D: 🔛🔛🔛====点击XML关键词开始====🔛🔛🔛
14:48:37.814/D: 要点击的关键词模式: "*小红书*"
14:48:37.815/D: 🔍 通配符匹配详情: 文字="小红书《APP》", 模式="*小红书*", 正则=".*小红书.*"
14:48:37.815/D: 🔍 正则匹配结果: true
14:48:37.816/D: ✅ 找到匹配项: "小红书《APP》" 匹配模式: "*小红书*" 匹配类型: 模糊匹配
14:48:37.816/D: 🎯 准备点击: "小红书《APP》" 坐标: (397, 1676) 偏移: (0, 0) 次数: 1 间隔: 50ms
14:48:37.816/D: su -c 'input tap 394.5 1677'
14:48:37.880/D: ✅ 点击执行完成
14:48:37.880/D: 🔚🔚🔚====点击XML关键词结束====🔚🔚🔚
14:48:38.881/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:38.881/D: 参数：必须关键词="通道①", 匹配关键词="小红书|*复扫*|*微信*|*通道*|*授权*|扫码只用于授权，不会登录你的 iPad 微信", 匹配数量=3, 排除关键词="*今日头条*"
14:48:38.882/D: 获取新的XML内容
14:48:38.882/D: 开始获取界面 XML...
14:48:41.185/D: 成功获取 XML 文件，大小: 3531 字节
14:48:41.185/D: 开始解析XML获取所有文字...
14:48:41.186/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:41.186/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:41.187/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:41.187/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:48:41.188/D: 从content-desc属性提取到文字: "转到上一层级"
14:48:41.188/D: 找到bounds: bounds="[0,96][154,206]"
14:48:41.189/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:48:41.189/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:41.190/D: 从text属性提取到文字: "平台列表"
14:48:41.190/D: 找到bounds: bounds="[198,125][350,177]"
14:48:41.191/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:48:41.192/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:48:41.192/D: 从content-desc属性提取到文字: "与梦"
14:48:41.192/D: 找到bounds: bounds="[948,96][1080,206]"
14:48:41.193/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:48:41.194/D: 找到节点: <node NAF="true" index="3" text="" resource-id="com.wb.www.yytt:id/wxWebview" class="android.webkit....
14:48:41.194/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:41.195/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:41.195/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:48:41.196/D: 从text属性提取到文字: "通道①"
14:48:41.196/D: 找到bounds: bounds="[0,2066][216,2174]"
14:48:41.197/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:48:41.197/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:48:41.198/D: 从text属性提取到文字: "通道②"
14:48:41.198/D: 找到bounds: bounds="[216,2066][432,2174]"
14:48:41.199/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:48:41.200/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:48:41.200/D: 从text属性提取到文字: "通道③"
14:48:41.201/D: 找到bounds: bounds="[432,2066][648,2174]"
14:48:41.201/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:48:41.202/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:48:41.203/D: 从text属性提取到文字: "复扫"
14:48:41.203/D: 找到bounds: bounds="[648,2066][864,2174]"
14:48:41.204/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:48:41.205/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:48:41.205/D: 从text属性提取到文字: "显示二维码"
14:48:41.206/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:48:41.206/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:41.207/D: XML解析成功，共提取到 8 个文字项
14:48:41.207/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:48:41.208/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:48:41.208/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:48:41.209/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:48:41.209/D: 文字项4: "通道①" 坐标: [0,2066][216,2174]
14:48:41.209/D: 文字项5: "通道②" 坐标: [216,2066][432,2174]
14:48:41.210/D: 文字项6: "通道③" 坐标: [432,2066][648,2174]
14:48:41.210/D: 文字项7: "复扫" 坐标: [648,2066][864,2174]
14:48:41.210/D: 文字项8: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:41.211/D: 匹配模式列表: ["小红书", "*复扫*", "*微信*", "*通道*", "*授权*", "扫码只用于授权，不会登录你的 iPad 微信"]
14:48:41.212/D: 正在检查文字 "通道①" 是否匹配模式...
14:48:41.212/D:   模式 "小红书" 匹配结果: false
14:48:41.213/D: 🔍 通配符匹配详情: 文字="通道①", 模式="*复扫*", 正则=".*复扫.*"
14:48:41.213/D: 🔍 正则匹配结果: false
14:48:41.214/D:   模式 "*复扫*" 匹配结果: false
14:48:41.214/D: 🔍 通配符匹配详情: 文字="通道①", 模式="*微信*", 正则=".*微信.*"
14:48:41.215/D: 🔍 正则匹配结果: false
14:48:41.215/D:   模式 "*微信*" 匹配结果: false
14:48:41.216/D: 🔍 通配符匹配详情: 文字="通道①", 模式="*通道*", 正则=".*通道.*"
14:48:41.216/D: 🔍 正则匹配结果: true
14:48:41.217/D:   模式 "*通道*" 匹配结果: true
14:48:41.218/D: 🔍 通配符匹配详情: 文字="通道①", 模式="*今日头条*", 正则=".*今日头条.*"
14:48:41.218/D: 🔍 正则匹配结果: false
14:48:41.219/D: ✅ 匹配到文字: "通道①" 坐标: [0,2066][216,2174] 原因: 必须关键词 + 匹配关键词
14:48:41.220/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 1 个结果====🔚🔚🔚
14:48:41.220/D: 🔛🔛🔛====点击XML关键词开始====🔛🔛🔛
14:48:41.220/D: 要点击的关键词模式: "通道①"
14:48:41.221/D: ✅ 找到匹配项: "通道①" 匹配模式: "通道①" 匹配类型: 精确匹配
14:48:41.222/D: 🎯 准备点击: "通道①" 坐标: (108, 2120) 偏移: (0, 0) 次数: 1 间隔: 50ms
14:48:41.222/D: su -c 'input tap 104 2117'
14:48:41.282/D: ✅ 点击执行完成
14:48:41.283/D: 🔚🔚🔚====点击XML关键词结束====🔚🔚🔚
14:48:51.283/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:51.284/D: 参数：必须关键词="拒绝", 匹配关键词="本次运行允许|仅在使用中允许", 匹配数量=2, 排除关键词=""
14:48:51.284/D: 获取新的XML内容
14:48:51.284/D: 开始获取界面 XML...
14:48:53.624/D: 成功获取 XML 文件，大小: 7435 字节
14:48:53.625/D: 开始解析XML获取所有文字...
14:48:53.625/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:53.626/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:53.626/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:53.626/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:48:53.627/D: 从content-desc属性提取到文字: "转到上一层级"
14:48:53.627/D: 找到bounds: bounds="[0,96][154,206]"
14:48:53.627/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:48:53.628/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:53.628/D: 从text属性提取到文字: "平台列表"
14:48:53.629/D: 找到bounds: bounds="[198,125][350,177]"
14:48:53.629/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:48:53.629/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:48:53.630/D: 从content-desc属性提取到文字: "与梦"
14:48:53.630/D: 找到bounds: bounds="[948,96][1080,206]"
14:48:53.630/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:48:53.631/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:48:53.632/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:53.632/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:53.632/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:48:53.632/D: 从text属性提取到文字: "微信登录"
14:48:53.633/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:53.633/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:48:53.633/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:53.634/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:53.634/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:53.634/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:53.634/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:53.635/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:53.635/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:48:53.635/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:48:53.636/D: 找到bounds: bounds="[132,555][951,742]"
14:48:53.636/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:48:53.636/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:48:53.637/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:48:53.637/D: 找到bounds: bounds="[291,767][789,1264]"
14:48:53.637/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:48:53.638/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:48:53.638/D: 从text属性提取到文字: "小红书"
14:48:53.638/D: 找到bounds: bounds="[132,1273][951,1347]"
14:48:53.639/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:48:53.639/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:53.640/D: 找到bounds: bounds="[132,1592][951,1707]"
14:48:53.640/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:53.640/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:48:53.641/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:48:53.641/D: 找到bounds: bounds="[132,1633][951,1707]"
14:48:53.641/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:48:53.642/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:53.642/D: 找到bounds: bounds="[132,1729][951,1803]"
14:48:53.642/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:53.643/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:48:53.643/D: 从content-desc属性提取到文字: "取消登录"
14:48:53.644/D: 找到bounds: bounds="[451,1737][629,1798]"
14:48:53.644/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:48:53.644/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:53.645/D: 从text属性提取到文字: "取消登录"
14:48:53.645/D: 找到bounds: bounds="[451,1737][629,1798]"
14:48:53.645/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:48:53.646/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:48:53.646/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:48:53.646/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:48:53.647/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:48:53.647/D: 从text属性提取到文字: "通道①"
14:48:53.647/D: 找到bounds: bounds="[0,2066][216,2174]"
14:48:53.647/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:48:53.648/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:48:53.648/D: 从text属性提取到文字: "通道②"
14:48:53.649/D: 找到bounds: bounds="[216,2066][432,2174]"
14:48:53.649/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:48:53.649/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:48:53.650/D: 从text属性提取到文字: "通道③"
14:48:53.650/D: 找到bounds: bounds="[432,2066][648,2174]"
14:48:53.650/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:48:53.651/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:48:53.651/D: 从text属性提取到文字: "复扫"
14:48:53.651/D: 找到bounds: bounds="[648,2066][864,2174]"
14:48:53.652/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:48:53.652/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:48:53.653/D: 从text属性提取到文字: "显示二维码"
14:48:53.653/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:48:53.653/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:53.654/D: XML解析成功，共提取到 15 个文字项
14:48:53.655/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:48:53.655/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:48:53.655/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:48:53.656/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:48:53.656/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:48:53.656/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:48:53.656/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:48:53.657/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:48:53.657/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:48:53.657/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:48:53.657/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:48:53.657/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:48:53.658/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:48:53.658/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:48:53.658/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:48:53.658/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:53.659/D: 匹配模式列表: ["本次运行允许", "仅在使用中允许"]
14:48:53.660/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:53.660/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:53.660/D: 参数：必须关键词="拒绝", 匹配关键词="本次运行允许|仅在使用中允许", 匹配数量=2, 排除关键词=""
14:48:53.660/D: 获取新的XML内容
14:48:53.661/D: 开始获取界面 XML...
14:48:55.937/D: 成功获取 XML 文件，大小: 7435 字节
14:48:55.937/D: 开始解析XML获取所有文字...
14:48:55.938/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:55.938/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:55.938/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:55.939/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:48:55.939/D: 从content-desc属性提取到文字: "转到上一层级"
14:48:55.940/D: 找到bounds: bounds="[0,96][154,206]"
14:48:55.940/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:48:55.940/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:55.941/D: 从text属性提取到文字: "平台列表"
14:48:55.941/D: 找到bounds: bounds="[198,125][350,177]"
14:48:55.941/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:48:55.942/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:48:55.942/D: 从content-desc属性提取到文字: "与梦"
14:48:55.942/D: 找到bounds: bounds="[948,96][1080,206]"
14:48:55.943/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:48:55.943/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:48:55.944/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:55.944/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:55.944/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:48:55.945/D: 从text属性提取到文字: "微信登录"
14:48:55.945/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:55.945/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:48:55.946/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:55.946/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:55.946/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:55.947/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:55.947/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:55.947/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:55.947/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:48:55.948/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:48:55.948/D: 找到bounds: bounds="[132,555][951,742]"
14:48:55.948/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:48:55.949/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:48:55.949/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:48:55.949/D: 找到bounds: bounds="[291,767][789,1264]"
14:48:55.950/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:48:55.950/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:48:55.951/D: 从text属性提取到文字: "小红书"
14:48:55.951/D: 找到bounds: bounds="[132,1273][951,1347]"
14:48:55.951/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:48:55.952/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:55.952/D: 找到bounds: bounds="[132,1592][951,1707]"
14:48:55.952/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:55.953/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:48:55.953/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:48:55.953/D: 找到bounds: bounds="[132,1633][951,1707]"
14:48:55.954/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:48:55.954/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:55.955/D: 找到bounds: bounds="[132,1729][951,1803]"
14:48:55.955/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:55.955/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:48:55.956/D: 从content-desc属性提取到文字: "取消登录"
14:48:55.956/D: 找到bounds: bounds="[451,1737][629,1798]"
14:48:55.956/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:48:55.957/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:55.957/D: 从text属性提取到文字: "取消登录"
14:48:55.957/D: 找到bounds: bounds="[451,1737][629,1798]"
14:48:55.957/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:48:55.958/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:48:55.958/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:48:55.959/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:48:55.959/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:48:55.960/D: 从text属性提取到文字: "通道①"
14:48:55.960/D: 找到bounds: bounds="[0,2066][216,2174]"
14:48:55.960/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:48:55.961/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:48:55.962/D: 从text属性提取到文字: "通道②"
14:48:55.962/D: 找到bounds: bounds="[216,2066][432,2174]"
14:48:55.962/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:48:55.963/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:48:55.963/D: 从text属性提取到文字: "通道③"
14:48:55.964/D: 找到bounds: bounds="[432,2066][648,2174]"
14:48:55.964/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:48:55.965/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:48:55.965/D: 从text属性提取到文字: "复扫"
14:48:55.965/D: 找到bounds: bounds="[648,2066][864,2174]"
14:48:55.966/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:48:55.966/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:48:55.967/D: 从text属性提取到文字: "显示二维码"
14:48:55.967/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:48:55.967/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:55.968/D: XML解析成功，共提取到 15 个文字项
14:48:55.968/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:48:55.969/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:48:55.969/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:48:55.969/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:48:55.969/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:48:55.969/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:48:55.970/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:48:55.970/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:48:55.970/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:48:55.970/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:48:55.970/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:48:55.971/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:48:55.971/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:48:55.971/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:48:55.971/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:48:55.971/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:55.972/D: 匹配模式列表: ["本次运行允许", "仅在使用中允许"]
14:48:55.973/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:55.973/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:55.973/D: 参数：必须关键词="拒绝", 匹配关键词="本次运行允许|仅在使用中允许", 匹配数量=2, 排除关键词=""
14:48:55.973/D: 获取新的XML内容
14:48:55.974/D: 开始获取界面 XML...
14:48:58.269/D: 成功获取 XML 文件，大小: 7435 字节
14:48:58.270/D: 开始解析XML获取所有文字...
14:48:58.270/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:48:58.271/D: 找到bounds: bounds="[0,0][1080,2174]"
14:48:58.271/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:58.272/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:48:58.272/D: 从content-desc属性提取到文字: "转到上一层级"
14:48:58.272/D: 找到bounds: bounds="[0,96][154,206]"
14:48:58.273/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:48:58.273/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:58.274/D: 从text属性提取到文字: "平台列表"
14:48:58.274/D: 找到bounds: bounds="[198,125][350,177]"
14:48:58.274/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:48:58.275/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:48:58.275/D: 从content-desc属性提取到文字: "与梦"
14:48:58.275/D: 找到bounds: bounds="[948,96][1080,206]"
14:48:58.276/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:48:58.276/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:48:58.277/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:58.277/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:58.277/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:48:58.277/D: 从text属性提取到文字: "微信登录"
14:48:58.278/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:58.278/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:48:58.278/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:58.279/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:58.279/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:58.279/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:58.280/D: 找到bounds: bounds="[0,206][1080,2174]"
14:48:58.280/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:58.280/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:48:58.281/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:48:58.281/D: 找到bounds: bounds="[132,555][951,742]"
14:48:58.282/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:48:58.282/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:48:58.283/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:48:58.283/D: 找到bounds: bounds="[291,767][789,1264]"
14:48:58.283/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:48:58.284/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:48:58.284/D: 从text属性提取到文字: "小红书"
14:48:58.285/D: 找到bounds: bounds="[132,1273][951,1347]"
14:48:58.285/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:48:58.286/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:58.286/D: 找到bounds: bounds="[132,1592][951,1707]"
14:48:58.286/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:58.287/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:48:58.287/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:48:58.288/D: 找到bounds: bounds="[132,1633][951,1707]"
14:48:58.288/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:48:58.289/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:48:58.289/D: 找到bounds: bounds="[132,1729][951,1803]"
14:48:58.290/D: 节点有坐标但无有效文字: text="" content-desc=""
14:48:58.290/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:48:58.291/D: 从content-desc属性提取到文字: "取消登录"
14:48:58.291/D: 找到bounds: bounds="[451,1737][629,1798]"
14:48:58.291/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:48:58.292/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:48:58.292/D: 从text属性提取到文字: "取消登录"
14:48:58.293/D: 找到bounds: bounds="[451,1737][629,1798]"
14:48:58.293/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:48:58.294/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:48:58.294/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:48:58.294/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:48:58.295/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:48:58.295/D: 从text属性提取到文字: "通道①"
14:48:58.296/D: 找到bounds: bounds="[0,2066][216,2174]"
14:48:58.296/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:48:58.297/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:48:58.297/D: 从text属性提取到文字: "通道②"
14:48:58.297/D: 找到bounds: bounds="[216,2066][432,2174]"
14:48:58.297/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:48:58.298/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:48:58.299/D: 从text属性提取到文字: "通道③"
14:48:58.299/D: 找到bounds: bounds="[432,2066][648,2174]"
14:48:58.299/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:48:58.300/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:48:58.300/D: 从text属性提取到文字: "复扫"
14:48:58.301/D: 找到bounds: bounds="[648,2066][864,2174]"
14:48:58.301/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:48:58.302/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:48:58.302/D: 从text属性提取到文字: "显示二维码"
14:48:58.303/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:48:58.303/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:58.303/D: XML解析成功，共提取到 15 个文字项
14:48:58.304/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:48:58.304/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:48:58.305/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:48:58.305/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:48:58.305/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:48:58.305/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:48:58.305/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:48:58.306/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:48:58.306/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:48:58.306/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:48:58.306/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:48:58.307/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:48:58.307/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:48:58.307/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:48:58.307/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:48:58.307/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:48:58.308/D: 匹配模式列表: ["本次运行允许", "仅在使用中允许"]
14:48:58.309/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:48:58.309/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:48:58.309/D: 参数：必须关键词="拒绝", 匹配关键词="本次运行允许|仅在使用中允许", 匹配数量=2, 排除关键词=""
14:48:58.310/D: 获取新的XML内容
14:48:58.310/D: 开始获取界面 XML...
14:49:00.666/D: 成功获取 XML 文件，大小: 7435 字节
14:49:00.666/D: 开始解析XML获取所有文字...
14:49:00.667/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:00.668/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:00.668/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:00.668/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:00.669/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:00.669/D: 找到bounds: bounds="[0,96][154,206]"
14:49:00.670/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:00.670/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:00.671/D: 从text属性提取到文字: "平台列表"
14:49:00.671/D: 找到bounds: bounds="[198,125][350,177]"
14:49:00.672/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:00.672/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:00.673/D: 从content-desc属性提取到文字: "与梦"
14:49:00.673/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:00.674/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:00.674/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:00.675/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:00.675/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:00.676/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:00.676/D: 从text属性提取到文字: "微信登录"
14:49:00.676/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:00.677/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:00.677/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:00.678/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:00.678/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:00.679/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:00.679/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:00.680/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:00.680/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:00.681/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:00.681/D: 找到bounds: bounds="[132,555][951,742]"
14:49:00.682/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:00.683/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:00.684/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:00.684/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:00.685/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:00.685/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:00.686/D: 从text属性提取到文字: "小红书"
14:49:00.686/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:00.687/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:00.688/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:00.689/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:00.689/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:00.690/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:00.690/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:00.691/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:00.692/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:00.693/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:00.693/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:00.694/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:00.694/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:00.695/D: 从content-desc属性提取到文字: "取消登录"
14:49:00.696/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:00.696/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:00.697/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:00.698/D: 从text属性提取到文字: "取消登录"
14:49:00.698/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:00.699/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:00.700/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:00.700/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:00.701/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:00.701/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:00.702/D: 从text属性提取到文字: "通道①"
14:49:00.702/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:00.703/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:00.704/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:00.705/D: 从text属性提取到文字: "通道②"
14:49:00.705/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:00.706/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:00.706/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:00.707/D: 从text属性提取到文字: "通道③"
14:49:00.708/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:00.708/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:00.709/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:00.710/D: 从text属性提取到文字: "复扫"
14:49:00.710/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:00.710/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:00.711/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:00.712/D: 从text属性提取到文字: "显示二维码"
14:49:00.712/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:00.713/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:00.714/D: XML解析成功，共提取到 15 个文字项
14:49:00.714/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:00.715/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:00.715/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:00.716/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:00.716/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:00.717/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:00.717/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:00.718/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:00.718/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:00.718/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:00.719/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:00.719/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:00.720/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:00.720/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:00.720/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:00.721/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:00.721/D: 匹配模式列表: ["本次运行允许", "仅在使用中允许"]
14:49:00.722/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:00.723/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:00.723/D: 参数：必须关键词="拒绝", 匹配关键词="本次运行允许|仅在使用中允许", 匹配数量=2, 排除关键词=""
14:49:00.724/D: 获取新的XML内容
14:49:00.724/D: 开始获取界面 XML...
14:49:03.065/D: 成功获取 XML 文件，大小: 7435 字节
14:49:03.065/D: 开始解析XML获取所有文字...
14:49:03.065/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:03.066/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:03.066/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:03.066/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:03.066/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:03.067/D: 找到bounds: bounds="[0,96][154,206]"
14:49:03.067/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:03.068/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:03.068/D: 从text属性提取到文字: "平台列表"
14:49:03.068/D: 找到bounds: bounds="[198,125][350,177]"
14:49:03.069/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:03.069/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:03.069/D: 从content-desc属性提取到文字: "与梦"
14:49:03.070/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:03.070/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:03.071/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:03.071/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:03.071/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:03.072/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:03.072/D: 从text属性提取到文字: "微信登录"
14:49:03.072/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:03.073/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:03.073/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:03.074/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:03.074/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:03.074/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:03.074/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:03.075/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:03.075/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:03.075/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:03.075/D: 找到bounds: bounds="[132,555][951,742]"
14:49:03.076/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:03.076/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:03.077/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:03.077/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:03.077/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:03.078/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:03.078/D: 从text属性提取到文字: "小红书"
14:49:03.078/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:03.079/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:03.080/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:03.080/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:03.080/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:03.081/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:03.081/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:03.081/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:03.082/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:03.082/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:03.083/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:03.083/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:03.083/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:03.084/D: 从content-desc属性提取到文字: "取消登录"
14:49:03.084/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:03.084/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:03.085/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:03.085/D: 从text属性提取到文字: "取消登录"
14:49:03.085/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:03.086/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:03.086/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:03.087/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:03.087/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:03.087/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:03.088/D: 从text属性提取到文字: "通道①"
14:49:03.088/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:03.088/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:03.089/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:03.089/D: 从text属性提取到文字: "通道②"
14:49:03.089/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:03.090/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:03.090/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:03.091/D: 从text属性提取到文字: "通道③"
14:49:03.091/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:03.092/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:03.092/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:03.093/D: 从text属性提取到文字: "复扫"
14:49:03.093/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:03.093/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:03.094/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:03.095/D: 从text属性提取到文字: "显示二维码"
14:49:03.095/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:03.095/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:03.096/D: XML解析成功，共提取到 15 个文字项
14:49:03.096/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:03.097/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:03.097/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:03.097/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:03.097/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:03.097/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:03.098/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:03.098/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:03.098/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:03.098/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:03.098/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:03.099/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:03.099/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:03.099/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:03.099/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:03.099/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:03.100/D: 匹配模式列表: ["本次运行允许", "仅在使用中允许"]
14:49:03.101/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:03.101/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:03.101/D: 参数：必须关键词="拒绝", 匹配关键词="本次运行允许|仅在使用中允许", 匹配数量=2, 排除关键词=""
14:49:03.102/D: 获取新的XML内容
14:49:03.102/D: 开始获取界面 XML...
14:49:05.449/D: 成功获取 XML 文件，大小: 7435 字节
14:49:05.450/D: 开始解析XML获取所有文字...
14:49:05.450/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:05.451/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:05.451/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:05.452/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:05.452/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:05.452/D: 找到bounds: bounds="[0,96][154,206]"
14:49:05.453/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:05.453/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:05.454/D: 从text属性提取到文字: "平台列表"
14:49:05.454/D: 找到bounds: bounds="[198,125][350,177]"
14:49:05.455/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:05.455/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:05.456/D: 从content-desc属性提取到文字: "与梦"
14:49:05.456/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:05.457/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:05.457/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:05.458/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:05.458/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:05.458/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:05.459/D: 从text属性提取到文字: "微信登录"
14:49:05.459/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:05.459/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:05.460/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:05.461/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:05.461/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:05.461/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:05.462/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:05.462/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:05.463/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:05.463/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:05.463/D: 找到bounds: bounds="[132,555][951,742]"
14:49:05.464/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:05.464/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:05.465/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:05.465/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:05.465/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:05.466/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:05.466/D: 从text属性提取到文字: "小红书"
14:49:05.466/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:05.467/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:05.468/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:05.468/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:05.468/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:05.469/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:05.469/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:05.469/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:05.470/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:05.471/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:05.471/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:05.472/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:05.472/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:05.473/D: 从content-desc属性提取到文字: "取消登录"
14:49:05.473/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:05.473/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:05.474/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:05.475/D: 从text属性提取到文字: "取消登录"
14:49:05.475/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:05.475/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:05.476/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:05.477/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:05.477/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:05.477/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:05.478/D: 从text属性提取到文字: "通道①"
14:49:05.478/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:05.479/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:05.479/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:05.480/D: 从text属性提取到文字: "通道②"
14:49:05.480/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:05.481/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:05.481/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:05.482/D: 从text属性提取到文字: "通道③"
14:49:05.483/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:05.483/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:05.484/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:05.485/D: 从text属性提取到文字: "复扫"
14:49:05.485/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:05.486/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:05.487/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:05.488/D: 从text属性提取到文字: "显示二维码"
14:49:05.488/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:05.488/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:05.489/D: XML解析成功，共提取到 15 个文字项
14:49:05.490/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:05.490/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:05.491/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:05.491/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:05.491/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:05.492/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:05.492/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:05.492/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:05.493/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:05.493/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:05.493/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:05.494/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:05.494/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:05.495/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:05.496/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:05.496/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:05.497/D: 匹配模式列表: ["本次运行允许", "仅在使用中允许"]
14:49:05.498/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:05.499/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:05.499/D: 参数：必须关键词="账号下线提示", 匹配关键词="", 匹配数量=1, 排除关键词=""
14:49:05.499/D: 使用缓存的XML内容
14:49:05.500/D: 开始获取界面 XML...
14:49:07.806/D: 成功获取 XML 文件，大小: 7435 字节
14:49:07.807/D: 开始解析XML获取所有文字...
14:49:07.807/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:07.808/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:07.808/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:07.808/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:07.809/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:07.809/D: 找到bounds: bounds="[0,96][154,206]"
14:49:07.809/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:07.810/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:07.810/D: 从text属性提取到文字: "平台列表"
14:49:07.810/D: 找到bounds: bounds="[198,125][350,177]"
14:49:07.810/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:07.811/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:07.811/D: 从content-desc属性提取到文字: "与梦"
14:49:07.812/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:07.812/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:07.812/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:07.813/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:07.813/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:07.813/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:07.813/D: 从text属性提取到文字: "微信登录"
14:49:07.814/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:07.814/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:07.815/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:07.815/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:07.815/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:07.815/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:07.816/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:07.816/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:07.816/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:07.816/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:07.817/D: 找到bounds: bounds="[132,555][951,742]"
14:49:07.817/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:07.817/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:07.818/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:07.818/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:07.818/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:07.819/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:07.819/D: 从text属性提取到文字: "小红书"
14:49:07.819/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:07.820/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:07.820/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:07.820/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:07.821/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:07.821/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:07.821/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:07.821/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:07.821/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:07.822/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:07.822/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:07.822/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:07.823/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:07.823/D: 从content-desc属性提取到文字: "取消登录"
14:49:07.824/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:07.824/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:07.824/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:07.825/D: 从text属性提取到文字: "取消登录"
14:49:07.825/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:07.825/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:07.826/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:07.826/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:07.826/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:07.827/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:07.827/D: 从text属性提取到文字: "通道①"
14:49:07.827/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:07.827/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:07.828/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:07.828/D: 从text属性提取到文字: "通道②"
14:49:07.828/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:07.829/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:07.829/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:07.830/D: 从text属性提取到文字: "通道③"
14:49:07.830/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:07.830/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:07.830/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:07.831/D: 从text属性提取到文字: "复扫"
14:49:07.831/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:07.831/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:07.832/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:07.832/D: 从text属性提取到文字: "显示二维码"
14:49:07.832/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:07.833/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:07.833/D: XML解析成功，共提取到 15 个文字项
14:49:07.833/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:07.834/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:07.834/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:07.834/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:07.834/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:07.835/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:07.835/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:07.835/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:07.835/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:07.835/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:07.836/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:07.836/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:07.836/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:07.836/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:07.837/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:07.837/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:07.837/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:07.838/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:07.838/D: 参数：必须关键词="账号下线提示", 匹配关键词="", 匹配数量=1, 排除关键词=""
14:49:07.838/D: 使用缓存的XML内容
14:49:07.838/D: 开始获取界面 XML...
14:49:10.131/D: 成功获取 XML 文件，大小: 7435 字节
14:49:10.132/D: 开始解析XML获取所有文字...
14:49:10.132/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:10.133/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:10.133/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:10.133/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:10.133/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:10.134/D: 找到bounds: bounds="[0,96][154,206]"
14:49:10.134/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:10.135/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:10.135/D: 从text属性提取到文字: "平台列表"
14:49:10.135/D: 找到bounds: bounds="[198,125][350,177]"
14:49:10.135/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:10.136/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:10.136/D: 从content-desc属性提取到文字: "与梦"
14:49:10.136/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:10.137/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:10.137/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:10.138/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:10.138/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:10.138/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:10.138/D: 从text属性提取到文字: "微信登录"
14:49:10.138/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:10.139/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:10.139/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:10.140/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:10.140/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:10.140/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:10.140/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:10.141/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:10.141/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:10.141/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:10.141/D: 找到bounds: bounds="[132,555][951,742]"
14:49:10.142/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:10.142/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:10.142/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:10.143/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:10.143/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:10.143/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:10.144/D: 从text属性提取到文字: "小红书"
14:49:10.144/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:10.144/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:10.145/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:10.145/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:10.146/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:10.146/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:10.146/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:10.146/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:10.147/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:10.147/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:10.148/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:10.148/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:10.148/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:10.148/D: 从content-desc属性提取到文字: "取消登录"
14:49:10.149/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:10.149/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:10.149/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:10.150/D: 从text属性提取到文字: "取消登录"
14:49:10.150/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:10.150/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:10.151/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:10.151/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:10.151/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:10.152/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:10.152/D: 从text属性提取到文字: "通道①"
14:49:10.152/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:10.153/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:10.153/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:10.153/D: 从text属性提取到文字: "通道②"
14:49:10.154/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:10.154/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:10.154/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:10.155/D: 从text属性提取到文字: "通道③"
14:49:10.155/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:10.155/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:10.156/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:10.156/D: 从text属性提取到文字: "复扫"
14:49:10.157/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:10.157/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:10.157/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:10.158/D: 从text属性提取到文字: "显示二维码"
14:49:10.158/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:10.158/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:10.159/D: XML解析成功，共提取到 15 个文字项
14:49:10.159/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:10.159/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:10.160/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:10.160/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:10.160/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:10.160/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:10.160/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:10.160/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:10.161/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:10.161/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:10.161/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:10.161/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:10.161/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:10.162/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:10.162/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:10.162/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:10.163/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:10.163/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:10.163/D: 参数：必须关键词="账号下线提示", 匹配关键词="", 匹配数量=1, 排除关键词=""
14:49:10.163/D: 使用缓存的XML内容
14:49:10.163/D: 开始获取界面 XML...
14:49:12.515/D: 成功获取 XML 文件，大小: 7435 字节
14:49:12.515/D: 开始解析XML获取所有文字...
14:49:12.515/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:12.516/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:12.516/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:12.516/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:12.517/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:12.517/D: 找到bounds: bounds="[0,96][154,206]"
14:49:12.517/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:12.517/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:12.518/D: 从text属性提取到文字: "平台列表"
14:49:12.518/D: 找到bounds: bounds="[198,125][350,177]"
14:49:12.518/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:12.519/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:12.519/D: 从content-desc属性提取到文字: "与梦"
14:49:12.519/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:12.520/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:12.520/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:12.520/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:12.521/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:12.521/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:12.521/D: 从text属性提取到文字: "微信登录"
14:49:12.521/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:12.522/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:12.522/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:12.523/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:12.523/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:12.523/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:12.523/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:12.524/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:12.524/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:12.524/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:12.524/D: 找到bounds: bounds="[132,555][951,742]"
14:49:12.525/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:12.525/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:12.526/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:12.526/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:12.526/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:12.526/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:12.527/D: 从text属性提取到文字: "小红书"
14:49:12.527/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:12.528/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:12.528/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:12.528/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:12.529/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:12.529/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:12.529/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:12.529/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:12.530/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:12.530/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:12.530/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:12.531/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:12.531/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:12.531/D: 从content-desc属性提取到文字: "取消登录"
14:49:12.531/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:12.532/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:12.532/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:12.533/D: 从text属性提取到文字: "取消登录"
14:49:12.533/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:12.533/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:12.534/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:12.534/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:12.534/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:12.534/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:12.535/D: 从text属性提取到文字: "通道①"
14:49:12.535/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:12.535/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:12.536/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:12.536/D: 从text属性提取到文字: "通道②"
14:49:12.536/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:12.536/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:12.537/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:12.537/D: 从text属性提取到文字: "通道③"
14:49:12.537/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:12.538/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:12.538/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:12.538/D: 从text属性提取到文字: "复扫"
14:49:12.539/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:12.539/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:12.539/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:12.540/D: 从text属性提取到文字: "显示二维码"
14:49:12.540/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:12.540/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:12.541/D: XML解析成功，共提取到 15 个文字项
14:49:12.541/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:12.541/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:12.541/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:12.542/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:12.542/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:12.542/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:12.542/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:12.542/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:12.543/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:12.543/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:12.543/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:12.543/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:12.543/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:12.544/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:12.544/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:12.544/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:12.545/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:12.545/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:12.545/D: 参数：必须关键词="账号下线提示", 匹配关键词="", 匹配数量=1, 排除关键词=""
14:49:12.545/D: 使用缓存的XML内容
14:49:12.545/D: 开始获取界面 XML...
14:49:14.867/D: 成功获取 XML 文件，大小: 7435 字节
14:49:14.868/D: 开始解析XML获取所有文字...
14:49:14.868/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:14.869/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:14.869/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:14.869/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:14.869/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:14.870/D: 找到bounds: bounds="[0,96][154,206]"
14:49:14.870/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:14.870/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:14.871/D: 从text属性提取到文字: "平台列表"
14:49:14.871/D: 找到bounds: bounds="[198,125][350,177]"
14:49:14.871/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:14.872/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:14.872/D: 从content-desc属性提取到文字: "与梦"
14:49:14.872/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:14.872/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:14.873/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:14.873/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:14.873/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:14.874/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:14.874/D: 从text属性提取到文字: "微信登录"
14:49:14.874/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:14.875/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:14.875/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:14.876/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:14.876/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:14.876/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:14.876/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:14.876/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:14.877/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:14.877/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:14.877/D: 找到bounds: bounds="[132,555][951,742]"
14:49:14.877/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:14.878/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:14.878/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:14.879/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:14.879/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:14.880/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:14.880/D: 从text属性提取到文字: "小红书"
14:49:14.880/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:14.881/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:14.881/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:14.881/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:14.881/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:14.882/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:14.882/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:14.882/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:14.882/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:14.883/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:14.883/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:14.884/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:14.884/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:14.884/D: 从content-desc属性提取到文字: "取消登录"
14:49:14.884/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:14.885/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:14.885/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:14.886/D: 从text属性提取到文字: "取消登录"
14:49:14.886/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:14.886/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:14.887/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:14.887/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:14.887/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:14.887/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:14.888/D: 从text属性提取到文字: "通道①"
14:49:14.888/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:14.888/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:14.889/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:14.889/D: 从text属性提取到文字: "通道②"
14:49:14.889/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:14.889/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:14.890/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:14.890/D: 从text属性提取到文字: "通道③"
14:49:14.890/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:14.891/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:14.891/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:14.892/D: 从text属性提取到文字: "复扫"
14:49:14.892/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:14.892/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:14.893/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:14.893/D: 从text属性提取到文字: "显示二维码"
14:49:14.893/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:14.893/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:14.894/D: XML解析成功，共提取到 15 个文字项
14:49:14.894/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:14.894/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:14.895/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:14.895/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:14.895/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:14.895/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:14.895/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:14.896/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:14.896/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:14.896/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:14.896/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:14.896/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:14.897/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:14.897/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:14.897/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:14.897/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:14.898/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:14.898/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:14.898/D: 参数：必须关键词="账号下线提示", 匹配关键词="", 匹配数量=1, 排除关键词=""
14:49:14.898/D: 使用缓存的XML内容
14:49:14.899/D: 开始获取界面 XML...
14:49:17.174/D: 成功获取 XML 文件，大小: 7435 字节
14:49:17.174/D: 开始解析XML获取所有文字...
14:49:17.174/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:17.175/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:17.175/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:17.175/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:17.176/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:17.176/D: 找到bounds: bounds="[0,96][154,206]"
14:49:17.176/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:17.177/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:17.177/D: 从text属性提取到文字: "平台列表"
14:49:17.177/D: 找到bounds: bounds="[198,125][350,177]"
14:49:17.178/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:17.178/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:17.179/D: 从content-desc属性提取到文字: "与梦"
14:49:17.179/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:17.179/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:17.180/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:17.180/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:17.180/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:17.180/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:17.181/D: 从text属性提取到文字: "微信登录"
14:49:17.181/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:17.181/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:17.182/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:17.182/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:17.182/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:17.182/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:17.183/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:17.183/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:17.183/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:17.184/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:17.184/D: 找到bounds: bounds="[132,555][951,742]"
14:49:17.184/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:17.185/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:17.185/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:17.185/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:17.185/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:17.186/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:17.186/D: 从text属性提取到文字: "小红书"
14:49:17.186/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:17.186/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:17.187/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:17.188/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:17.188/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:17.188/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:17.189/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:17.189/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:17.189/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:17.190/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:17.190/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:17.190/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:17.190/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:17.191/D: 从content-desc属性提取到文字: "取消登录"
14:49:17.191/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:17.191/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:17.192/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:17.192/D: 从text属性提取到文字: "取消登录"
14:49:17.192/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:17.192/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:17.193/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:17.193/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:17.194/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:17.194/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:17.194/D: 从text属性提取到文字: "通道①"
14:49:17.194/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:17.195/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:17.195/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:17.196/D: 从text属性提取到文字: "通道②"
14:49:17.196/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:17.196/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:17.197/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:17.197/D: 从text属性提取到文字: "通道③"
14:49:17.197/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:17.197/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:17.198/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:17.198/D: 从text属性提取到文字: "复扫"
14:49:17.198/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:17.199/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:17.199/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:17.200/D: 从text属性提取到文字: "显示二维码"
14:49:17.200/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:17.200/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:17.201/D: XML解析成功，共提取到 15 个文字项
14:49:17.201/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:17.202/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:17.202/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:17.202/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:17.202/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:17.202/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:17.203/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:17.203/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:17.203/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:17.203/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:17.203/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:17.203/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:17.204/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:17.204/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:17.204/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:17.204/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:17.205/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:17.205/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:17.205/D: 参数：必须关键词="账号下线提示", 匹配关键词="", 匹配数量=1, 排除关键词=""
14:49:17.206/D: 使用缓存的XML内容
14:49:17.206/D: 开始获取界面 XML...
14:49:19.531/D: 成功获取 XML 文件，大小: 7435 字节
14:49:19.531/D: 开始解析XML获取所有文字...
14:49:19.532/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.wb.www.yytt" ...
14:49:19.532/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:19.533/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:19.533/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.ImageButton" package="com.wb.www.yytt" ...
14:49:19.533/D: 从content-desc属性提取到文字: "转到上一层级"
14:49:19.533/D: 找到bounds: bounds="[0,96][154,206]"
14:49:19.534/D: 添加文字项: "转到上一层级" 坐标: [0,96][154,206]
14:49:19.534/D: 找到节点: <node index="1" text="平台列表" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:19.535/D: 从text属性提取到文字: "平台列表"
14:49:19.535/D: 找到bounds: bounds="[198,125][350,177]"
14:49:19.535/D: 添加文字项: "平台列表" 坐标: [198,125][350,177]
14:49:19.536/D: 找到节点: <node index="2" text="" resource-id="com.wb.www.yytt:id/action_refresh" class="android.widget.TextVi...
14:49:19.536/D: 从content-desc属性提取到文字: "与梦"
14:49:19.536/D: 找到bounds: bounds="[948,96][1080,206]"
14:49:19.536/D: 添加文字项: "与梦" 坐标: [948,96][1080,206]
14:49:19.537/D: 找到节点: <node index="3" text="" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" cont...
14:49:19.537/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:19.537/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:19.538/D: 找到节点: <node index="0" text="微信登录" resource-id="" class="android.webkit.WebView" package="com.wb.www.yytt" ...
14:49:19.538/D: 从text属性提取到文字: "微信登录"
14:49:19.538/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:19.539/D: 添加文字项: "微信登录" 坐标: [0,206][1080,2174]
14:49:19.539/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:19.540/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:19.540/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:19.540/D: 找到节点: <node index="0" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:19.540/D: 找到bounds: bounds="[0,206][1080,2174]"
14:49:19.540/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:19.541/D: 找到节点: <node index="0" text="请使用手机微信扫描二维码&#10;授权登录以下应用" resource-id="" class="android.widget.TextView" pack...
14:49:19.541/D: 从text属性提取到文字: "请使用手机微信扫描二维码&#10;授权登录以下应用"
14:49:19.541/D: 找到bounds: bounds="[132,555][951,742]"
14:49:19.541/D: 添加文字项: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:19.542/D: 找到节点: <node index="1" text="011gEqaN3U9J0w3V" resource-id="" class="android.widget.Image" package="com.wb....
14:49:19.542/D: 从text属性提取到文字: "011gEqaN3U9J0w3V"
14:49:19.542/D: 找到bounds: bounds="[291,767][789,1264]"
14:49:19.543/D: 添加文字项: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:19.543/D: 找到节点: <node index="2" text="小红书" resource-id="" class="android.view.View" package="com.wb.www.yytt" conten...
14:49:19.544/D: 从text属性提取到文字: "小红书"
14:49:19.544/D: 找到bounds: bounds="[132,1273][951,1347]"
14:49:19.544/D: 添加文字项: "小红书" 坐标: [132,1273][951,1347]
14:49:19.545/D: 找到节点: <node index="3" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:19.545/D: 找到bounds: bounds="[132,1592][951,1707]"
14:49:19.545/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:19.545/D: 找到节点: <node index="0" text="扫码只用于授权，不会登录你的 iPad 微信" resource-id="" class="android.widget.TextView" package...
14:49:19.546/D: 从text属性提取到文字: "扫码只用于授权，不会登录你的 iPad 微信"
14:49:19.546/D: 找到bounds: bounds="[132,1633][951,1707]"
14:49:19.546/D: 添加文字项: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:19.546/D: 找到节点: <node index="4" text="" resource-id="" class="android.view.View" package="com.wb.www.yytt" content-d...
14:49:19.547/D: 找到bounds: bounds="[132,1729][951,1803]"
14:49:19.547/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:19.547/D: 找到节点: <node index="0" text="" resource-id="js_cancel_login" class="android.view.View" package="com.wb.www....
14:49:19.548/D: 从content-desc属性提取到文字: "取消登录"
14:49:19.548/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:19.548/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:19.548/D: 找到节点: <node index="0" text="取消登录" resource-id="" class="android.widget.TextView" package="com.wb.www.yytt"...
14:49:19.549/D: 从text属性提取到文字: "取消登录"
14:49:19.549/D: 找到bounds: bounds="[451,1737][629,1798]"
14:49:19.549/D: 添加文字项: "取消登录" 坐标: [451,1737][629,1798]
14:49:19.550/D: 找到节点: <node index="5" text="  " resource-id="" class="android.widget.TextView" package="com.wb.www.yytt" c...
14:49:19.550/D: 找到bounds: bounds="[1028,206][1028,2174]"
14:49:19.550/D: 节点有坐标但无有效文字: text="  " content-desc=""
14:49:19.551/D: 找到节点: <node index="4" text="通道①" resource-id="com.wb.www.yytt:id/wxKeyText" class="android.widget.TextView...
14:49:19.551/D: 从text属性提取到文字: "通道①"
14:49:19.551/D: 找到bounds: bounds="[0,2066][216,2174]"
14:49:19.552/D: 添加文字项: "通道①" 坐标: [0,2066][216,2174]
14:49:19.552/D: 找到节点: <node index="5" text="通道②" resource-id="com.wb.www.yytt:id/wxKeyText2" class="android.widget.TextVie...
14:49:19.553/D: 从text属性提取到文字: "通道②"
14:49:19.553/D: 找到bounds: bounds="[216,2066][432,2174]"
14:49:19.553/D: 添加文字项: "通道②" 坐标: [216,2066][432,2174]
14:49:19.554/D: 找到节点: <node index="6" text="通道③" resource-id="com.wb.www.yytt:id/wxKeyText1" class="android.widget.TextVie...
14:49:19.554/D: 从text属性提取到文字: "通道③"
14:49:19.554/D: 找到bounds: bounds="[432,2066][648,2174]"
14:49:19.554/D: 添加文字项: "通道③" 坐标: [432,2066][648,2174]
14:49:19.555/D: 找到节点: <node index="7" text="复扫" resource-id="com.wb.www.yytt:id/fusText" class="android.widget.TextView" p...
14:49:19.555/D: 从text属性提取到文字: "复扫"
14:49:19.556/D: 找到bounds: bounds="[648,2066][864,2174]"
14:49:19.556/D: 添加文字项: "复扫" 坐标: [648,2066][864,2174]
14:49:19.556/D: 找到节点: <node index="8" text="显示二维码" resource-id="com.wb.www.yytt:id/showQrText" class="android.widget.TextV...
14:49:19.557/D: 从text属性提取到文字: "显示二维码"
14:49:19.557/D: 找到bounds: bounds="[864,2066][1080,2174]"
14:49:19.557/D: 添加文字项: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:19.557/D: XML解析成功，共提取到 15 个文字项
14:49:19.558/D: 提取到的所有文字: "转到上一层级", "平台列表", "与梦", "微信登录", "请使用手机微信扫描二维码&#10;授权登录以下应用", "011gEqaN3U9J0w3V", "小红书", "扫码只用于授权，不会登录你的 iPad 微信", "取消登录", "取消登录", "通道①", "通道②", "通道③", "复扫", "显示二维码"
14:49:19.558/D: 文字项1: "转到上一层级" 坐标: [0,96][154,206]
14:49:19.558/D: 文字项2: "平台列表" 坐标: [198,125][350,177]
14:49:19.559/D: 文字项3: "与梦" 坐标: [948,96][1080,206]
14:49:19.559/D: 文字项4: "微信登录" 坐标: [0,206][1080,2174]
14:49:19.559/D: 文字项5: "请使用手机微信扫描二维码&#10;授权登录以下应用" 坐标: [132,555][951,742]
14:49:19.559/D: 文字项6: "011gEqaN3U9J0w3V" 坐标: [291,767][789,1264]
14:49:19.559/D: 文字项7: "小红书" 坐标: [132,1273][951,1347]
14:49:19.560/D: 文字项8: "扫码只用于授权，不会登录你的 iPad 微信" 坐标: [132,1633][951,1707]
14:49:19.560/D: 文字项9: "取消登录" 坐标: [451,1737][629,1798]
14:49:19.560/D: 文字项10: "取消登录" 坐标: [451,1737][629,1798]
14:49:19.560/D: 文字项11: "通道①" 坐标: [0,2066][216,2174]
14:49:19.560/D: 文字项12: "通道②" 坐标: [216,2066][432,2174]
14:49:19.560/D: 文字项13: "通道③" 坐标: [432,2066][648,2174]
14:49:19.561/D: 文字项14: "复扫" 坐标: [648,2066][864,2174]
14:49:19.561/D: 文字项15: "显示二维码" 坐标: [864,2066][1080,2174]
14:49:19.562/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
14:49:19.562/D: 开始获取界面 XML...
14:49:19.562/D: 开始获取界面 XML...
14:49:21.882/D: 成功获取 XML 文件，大小: 7435 字节
14:49:21.883/D: 界面 XML 导出成功
14:49:21.883/D: 开始解析 XML 中的文本元素...
14:49:21.890/D: 共找到 15 个有效元素 (已过滤 10 个空文本元素)
14:49:21.892/D: 找到 0 个首页关键词
14:49:24.402/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:24.402/D: 参数：必须关键词="*小红书*", 匹配关键词="*平台列表*|*今日头条*|*通道*|*APP*", 匹配数量=3, 排除关键词="*复扫*"
14:49:24.403/D: 使用缓存的XML内容
14:49:24.403/D: 开始获取界面 XML...
14:49:27.750/D: 成功获取 XML 文件，大小: 17150 字节
14:49:27.750/D: 开始解析XML获取所有文字...
14:49:27.751/D: 找到节点: <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.miui.home" co...
14:49:27.752/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:27.753/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:27.754/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/workspace" class="com.miui.home.launcher.Scree...
14:49:27.756/D: 找到bounds: bounds="[0,0][1080,2174]"
14:49:27.756/D: 节点有坐标但无有效文字: text="" content-desc=""
14:49:27.757/D: 找到节点: <node index="13" text="" resource-id="" class="android.widget.ImageView" package="com.miui.home" con...
14:49:27.758/D: 从content-desc属性提取到文字: "第1屏"
14:49:27.759/D: 找到bounds: bounds="[521,1852][558,1889]"
14:49:27.759/D: 添加文字项: "第1屏" 坐标: [521,1852][558,1889]
14:49:27.761/D: 找到节点: <node index="14" text="" resource-id="" class="android.widget.ImageView" package="com.miui.home" con...
14:49:27.761/D: 从content-desc属性提取到文字: "第2屏"
14:49:27.762/D: 找到bounds: bounds="[558,1852][595,1889]"
14:49:27.762/D: 添加文字项: "第2屏" 坐标: [558,1852][595,1889]
14:49:27.764/D: 找到节点: <node index="16" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.765/D: 从content-desc属性提取到文字: "米家"
14:49:27.766/D: 找到bounds: bounds="[33,164][287,439]"
14:49:27.767/D: 添加文字项: "米家" 坐标: [33,164][287,439]
14:49:27.769/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.770/D: 从content-desc属性提取到文字: "米家"
14:49:27.770/D: 找到bounds: bounds="[74,180][246,352]"
14:49:27.771/D: 添加文字项: "米家" 坐标: [74,180][246,352]
14:49:27.773/D: 找到节点: <node index="1" text="米家" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView" ...
14:49:27.774/D: 从text属性提取到文字: "米家"
14:49:27.774/D: 找到bounds: bounds="[33,357][287,439]"
14:49:27.775/D: 添加文字项: "米家" 坐标: [33,357][287,439]
14:49:27.778/D: 找到节点: <node index="17" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.779/D: 从content-desc属性提取到文字: "音乐"
14:49:27.780/D: 找到bounds: bounds="[287,164][541,439]"
14:49:27.781/D: 添加文字项: "音乐" 坐标: [287,164][541,439]
14:49:27.782/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.783/D: 从content-desc属性提取到文字: "音乐"
14:49:27.784/D: 找到bounds: bounds="[328,180][500,352]"
14:49:27.784/D: 添加文字项: "音乐" 坐标: [328,180][500,352]
14:49:27.786/D: 找到节点: <node index="1" text="音乐" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView" ...
14:49:27.788/D: 从text属性提取到文字: "音乐"
14:49:27.789/D: 找到bounds: bounds="[287,357][541,439]"
14:49:27.790/D: 添加文字项: "音乐" 坐标: [287,357][541,439]
14:49:27.791/D: 找到节点: <node index="18" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.793/D: 从content-desc属性提取到文字: "笔记"
14:49:27.793/D: 找到bounds: bounds="[541,164][795,439]"
14:49:27.794/D: 添加文字项: "笔记" 坐标: [541,164][795,439]
14:49:27.796/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.797/D: 从content-desc属性提取到文字: "笔记"
14:49:27.798/D: 找到bounds: bounds="[582,180][754,352]"
14:49:27.800/D: 添加文字项: "笔记" 坐标: [582,180][754,352]
14:49:27.801/D: 找到节点: <node index="1" text="笔记" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView" ...
14:49:27.803/D: 从text属性提取到文字: "笔记"
14:49:27.803/D: 找到bounds: bounds="[541,357][795,439]"
14:49:27.804/D: 添加文字项: "笔记" 坐标: [541,357][795,439]
14:49:27.806/D: 找到节点: <node index="19" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.807/D: 从content-desc属性提取到文字: "小米社区"
14:49:27.808/D: 找到bounds: bounds="[795,164][1049,439]"
14:49:27.809/D: 添加文字项: "小米社区" 坐标: [795,164][1049,439]
14:49:27.811/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.812/D: 从content-desc属性提取到文字: "小米社区"
14:49:27.813/D: 找到bounds: bounds="[836,180][1008,352]"
14:49:27.814/D: 添加文字项: "小米社区" 坐标: [836,180][1008,352]
14:49:27.816/D: 找到节点: <node index="1" text="小米社区" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView...
14:49:27.817/D: 从text属性提取到文字: "小米社区"
14:49:27.817/D: 找到bounds: bounds="[795,357][1049,439]"
14:49:27.817/D: 添加文字项: "小米社区" 坐标: [795,357][1049,439]
14:49:27.819/D: 找到节点: <node index="20" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.820/D: 从content-desc属性提取到文字: "小米有品"
14:49:27.820/D: 找到bounds: bounds="[33,445][287,720]"
14:49:27.821/D: 添加文字项: "小米有品" 坐标: [33,445][287,720]
14:49:27.823/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.824/D: 从content-desc属性提取到文字: "小米有品"
14:49:27.825/D: 找到bounds: bounds="[74,461][246,633]"
14:49:27.826/D: 添加文字项: "小米有品" 坐标: [74,461][246,633]
14:49:27.827/D: 找到节点: <node index="1" text="小米有品" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView...
14:49:27.828/D: 从text属性提取到文字: "小米有品"
14:49:27.828/D: 找到bounds: bounds="[33,638][287,720]"
14:49:27.829/D: 添加文字项: "小米有品" 坐标: [33,638][287,720]
14:49:27.829/D: 找到节点: <node index="21" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.830/D: 从content-desc属性提取到文字: "热门"
14:49:27.831/D: 找到bounds: bounds="[287,445][541,720]"
14:49:27.833/D: 添加文字项: "热门" 坐标: [287,445][541,720]
14:49:27.834/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.835/D: 从content-desc属性提取到文字: "热门"
14:49:27.836/D: 找到bounds: bounds="[328,461][500,633]"
14:49:27.836/D: 添加文字项: "热门" 坐标: [328,461][500,633]
14:49:27.837/D: 找到节点: <node index="1" text="热门" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView" ...
14:49:27.837/D: 从text属性提取到文字: "热门"
14:49:27.837/D: 找到bounds: bounds="[287,638][541,720]"
14:49:27.838/D: 添加文字项: "热门" 坐标: [287,638][541,720]
14:49:27.838/D: 找到节点: <node index="22" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.839/D: 从content-desc属性提取到文字: "小红书"
14:49:27.839/D: 找到bounds: bounds="[541,445][795,720]"
14:49:27.840/D: 添加文字项: "小红书" 坐标: [541,445][795,720]
14:49:27.840/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.841/D: 从content-desc属性提取到文字: "小红书"
14:49:27.842/D: 找到bounds: bounds="[582,461][754,633]"
14:49:27.844/D: 添加文字项: "小红书" 坐标: [582,461][754,633]
14:49:27.846/D: 找到节点: <node index="1" text="小红书" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView"...
14:49:27.847/D: 从text属性提取到文字: "小红书"
14:49:27.848/D: 找到bounds: bounds="[541,638][795,720]"
14:49:27.848/D: 添加文字项: "小红书" 坐标: [541,638][795,720]
14:49:27.850/D: 找到节点: <node index="23" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.850/D: 从content-desc属性提取到文字: "汽水音乐"
14:49:27.851/D: 找到bounds: bounds="[795,445][1049,720]"
14:49:27.851/D: 添加文字项: "汽水音乐" 坐标: [795,445][1049,720]
14:49:27.853/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.855/D: 从content-desc属性提取到文字: "汽水音乐"
14:49:27.856/D: 找到bounds: bounds="[836,461][1008,633]"
14:49:27.857/D: 添加文字项: "汽水音乐" 坐标: [836,461][1008,633]
14:49:27.859/D: 找到节点: <node index="1" text="汽水音乐" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView...
14:49:27.860/D: 从text属性提取到文字: "汽水音乐"
14:49:27.860/D: 找到bounds: bounds="[795,638][1049,720]"
14:49:27.861/D: 添加文字项: "汽水音乐" 坐标: [795,638][1049,720]
14:49:27.861/D: 找到节点: <node index="24" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.862/D: 从content-desc属性提取到文字: "备份工具箱"
14:49:27.862/D: 找到bounds: bounds="[33,726][287,1001]"
14:49:27.863/D: 添加文字项: "备份工具箱" 坐标: [33,726][287,1001]
14:49:27.865/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.867/D: 从content-desc属性提取到文字: "备份工具箱"
14:49:27.867/D: 找到bounds: bounds="[74,742][246,914]"
14:49:27.868/D: 添加文字项: "备份工具箱" 坐标: [74,742][246,914]
14:49:27.870/D: 找到节点: <node index="1" text="备份工具箱" resource-id="com.miui.home:id/icon_title" class="android.widget.TextVie...
14:49:27.871/D: 从text属性提取到文字: "备份工具箱"
14:49:27.872/D: 找到bounds: bounds="[33,919][287,1001]"
14:49:27.873/D: 添加文字项: "备份工具箱" 坐标: [33,919][287,1001]
14:49:27.875/D: 找到节点: <node index="25" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.877/D: 从content-desc属性提取到文字: "与梦"
14:49:27.878/D: 找到bounds: bounds="[287,726][541,1001]"
14:49:27.879/D: 添加文字项: "与梦" 坐标: [287,726][541,1001]
14:49:27.880/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.882/D: 从content-desc属性提取到文字: "与梦"
14:49:27.882/D: 找到bounds: bounds="[328,742][500,914]"
14:49:27.883/D: 添加文字项: "与梦" 坐标: [328,742][500,914]
14:49:27.884/D: 找到节点: <node index="1" text="与梦" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView" ...
14:49:27.884/D: 从text属性提取到文字: "与梦"
14:49:27.884/D: 找到bounds: bounds="[287,919][541,1001]"
14:49:27.885/D: 添加文字项: "与梦" 坐标: [287,919][541,1001]
14:49:27.887/D: 找到节点: <node index="26" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.889/D: 从content-desc属性提取到文字: "微信"
14:49:27.889/D: 找到bounds: bounds="[541,726][795,1001]"
14:49:27.891/D: 添加文字项: "微信" 坐标: [541,726][795,1001]
14:49:27.892/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.894/D: 从content-desc属性提取到文字: "微信"
14:49:27.894/D: 找到bounds: bounds="[582,742][754,914]"
14:49:27.895/D: 添加文字项: "微信" 坐标: [582,742][754,914]
14:49:27.897/D: 找到节点: <node index="1" text="微信" resource-id="com.miui.home:id/icon_title" class="android.widget.TextView" ...
14:49:27.899/D: 从text属性提取到文字: "微信"
14:49:27.900/D: 找到bounds: bounds="[541,919][795,1001]"
14:49:27.901/D: 添加文字项: "微信" 坐标: [541,919][795,1001]
14:49:27.902/D: 找到节点: <node index="27" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.903/D: 从content-desc属性提取到文字: "AutoJs6"
14:49:27.904/D: 找到bounds: bounds="[795,726][1049,1001]"
14:49:27.905/D: 添加文字项: "AutoJs6" 坐标: [795,726][1049,1001]
14:49:27.906/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.908/D: 从content-desc属性提取到文字: "AutoJs6"
14:49:27.909/D: 找到bounds: bounds="[836,742][1008,914]"
14:49:27.910/D: 添加文字项: "AutoJs6" 坐标: [836,742][1008,914]
14:49:27.912/D: 找到节点: <node index="1" text="AutoJs6" resource-id="com.miui.home:id/icon_title" class="android.widget.TextV...
14:49:27.913/D: 从text属性提取到文字: "AutoJs6"
14:49:27.914/D: 找到bounds: bounds="[795,919][1049,1001]"
14:49:27.914/D: 添加文字项: "AutoJs6" 坐标: [795,919][1049,1001]
14:49:27.915/D: 找到节点: <node index="28" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home...
14:49:27.916/D: 从content-desc属性提取到文字: "AuthApp"
14:49:27.917/D: 找到bounds: bounds="[33,1007][287,1282]"
14:49:27.917/D: 添加文字项: "AuthApp" 坐标: [33,1007][287,1282]
14:49:27.919/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.920/D: 从content-desc属性提取到文字: "AuthApp"
14:49:27.920/D: 找到bounds: bounds="[74,1023][246,1195]"
14:49:27.922/D: 添加文字项: "AuthApp" 坐标: [74,1023][246,1195]
14:49:27.923/D: 找到节点: <node index="1" text="AuthApp" resource-id="com.miui.home:id/icon_title" class="android.widget.TextV...
14:49:27.924/D: 从text属性提取到文字: "AuthApp"
14:49:27.924/D: 找到bounds: bounds="[33,1200][287,1282]"
14:49:27.925/D: 添加文字项: "AuthApp" 坐标: [33,1200][287,1282]
14:49:27.925/D: 找到节点: <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home"...
14:49:27.926/D: 从content-desc属性提取到文字: "电话"
14:49:27.926/D: 找到bounds: bounds="[32,1908][286,2096]"
14:49:27.926/D: 添加文字项: "电话" 坐标: [32,1908][286,2096]
14:49:27.927/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.927/D: 从content-desc属性提取到文字: "电话"
14:49:27.928/D: 找到bounds: bounds="[73,1924][245,2096]"
14:49:27.928/D: 添加文字项: "电话" 坐标: [73,1924][245,2096]
14:49:27.929/D: 找到节点: <node index="2" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home"...
14:49:27.929/D: 从content-desc属性提取到文字: "短信"
14:49:27.930/D: 找到bounds: bounds="[286,1908][540,2096]"
14:49:27.931/D: 添加文字项: "短信" 坐标: [286,1908][540,2096]
14:49:27.932/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.933/D: 从content-desc属性提取到文字: "短信"
14:49:27.933/D: 找到bounds: bounds="[327,1924][499,2096]"
14:49:27.934/D: 添加文字项: "短信" 坐标: [327,1924][499,2096]
14:49:27.934/D: 找到节点: <node index="3" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home"...
14:49:27.935/D: 从content-desc属性提取到文字: "浏览器"
14:49:27.935/D: 找到bounds: bounds="[540,1908][794,2096]"
14:49:27.936/D: 添加文字项: "浏览器" 坐标: [540,1908][794,2096]
14:49:27.936/D: 找到节点: <node index="0" text="" resource-id="com.miui.home:id/icon_icon" class="android.widget.ImageView" pa...
14:49:27.937/D: 从content-desc属性提取到文字: "浏览器"
14:49:27.937/D: 找到bounds: bounds="[581,1924][753,2096]"
14:49:27.937/D: 添加文字项: "浏览器" 坐标: [581,1924][753,2096]
14:49:27.938/D: 找到节点: <node index="4" text="" resource-id="" class="android.widget.RelativeLayout" package="com.miui.home"...
14:49:27.939/D: 从content-desc属性提取到文字: "相机"
14:49:27.939/D: 找到bounds: bounds="[794,1908][1048,2096]"
14:49:27.939/D: 添加文字项: "相机" 坐标: [794,1908][1048,2096]
14:49:27.940/D: XML解析成功，共提取到 48 个文字项
14:49:27.941/D: 提取到的所有文字: "第1屏", "第2屏", "米家", "米家", "米家", "音乐", "音乐", "音乐", "笔记", "笔记", "笔记", "小米社区", "小米社区", "小米社区", "小米有品", "小米有品", "小米有品", "热门", "热门", "热门", "小红书", "小红书", "小红书", "汽水音乐", "汽水音乐", "汽水音乐", "备份工具箱", "备份工具箱", "备份工具箱", "与梦", "与梦", "与梦", "微信", "微信", "微信", "AutoJs6", "AutoJs6", "AutoJs6", "AuthApp", "AuthApp", "AuthApp", "电话", "电话", "短信", "短信", "浏览器", "浏览器", "相机"
14:49:27.943/D: 文字项1: "第1屏" 坐标: [521,1852][558,1889]
14:49:27.944/D: 文字项2: "第2屏" 坐标: [558,1852][595,1889]
14:49:27.945/D: 文字项3: "米家" 坐标: [33,164][287,439]
14:49:27.946/D: 文字项4: "米家" 坐标: [74,180][246,352]
14:49:27.947/D: 文字项5: "米家" 坐标: [33,357][287,439]
14:49:27.948/D: 文字项6: "音乐" 坐标: [287,164][541,439]
14:49:27.948/D: 文字项7: "音乐" 坐标: [328,180][500,352]
14:49:27.948/D: 文字项8: "音乐" 坐标: [287,357][541,439]
14:49:27.948/D: 文字项9: "笔记" 坐标: [541,164][795,439]
14:49:27.949/D: 文字项10: "笔记" 坐标: [582,180][754,352]
14:49:27.949/D: 文字项11: "笔记" 坐标: [541,357][795,439]
14:49:27.949/D: 文字项12: "小米社区" 坐标: [795,164][1049,439]
14:49:27.949/D: 文字项13: "小米社区" 坐标: [836,180][1008,352]
14:49:27.949/D: 文字项14: "小米社区" 坐标: [795,357][1049,439]
14:49:27.950/D: 文字项15: "小米有品" 坐标: [33,445][287,720]
14:49:27.950/D: 文字项16: "小米有品" 坐标: [74,461][246,633]
14:49:27.950/D: 文字项17: "小米有品" 坐标: [33,638][287,720]
14:49:27.950/D: 文字项18: "热门" 坐标: [287,445][541,720]
14:49:27.950/D: 文字项19: "热门" 坐标: [328,461][500,633]
14:49:27.951/D: 文字项20: "热门" 坐标: [287,638][541,720]
14:49:27.951/D: 文字项21: "小红书" 坐标: [541,445][795,720]
14:49:27.951/D: 文字项22: "小红书" 坐标: [582,461][754,633]
14:49:27.952/D: 文字项23: "小红书" 坐标: [541,638][795,720]
14:49:27.954/D: 文字项24: "汽水音乐" 坐标: [795,445][1049,720]
14:49:27.955/D: 文字项25: "汽水音乐" 坐标: [836,461][1008,633]
14:49:27.955/D: 文字项26: "汽水音乐" 坐标: [795,638][1049,720]
14:49:27.956/D: 文字项27: "备份工具箱" 坐标: [33,726][287,1001]
14:49:27.957/D: 文字项28: "备份工具箱" 坐标: [74,742][246,914]
14:49:27.958/D: 文字项29: "备份工具箱" 坐标: [33,919][287,1001]
14:49:27.959/D: 文字项30: "与梦" 坐标: [287,726][541,1001]
14:49:27.959/D: 文字项31: "与梦" 坐标: [328,742][500,914]
14:49:27.960/D: 文字项32: "与梦" 坐标: [287,919][541,1001]
14:49:27.960/D: 文字项33: "微信" 坐标: [541,726][795,1001]
14:49:27.960/D: 文字项34: "微信" 坐标: [582,742][754,914]
14:49:27.960/D: 文字项35: "微信" 坐标: [541,919][795,1001]
14:49:27.960/D: 文字项36: "AutoJs6" 坐标: [795,726][1049,1001]
14:49:27.961/D: 文字项37: "AutoJs6" 坐标: [836,742][1008,914]
14:49:27.961/D: 文字项38: "AutoJs6" 坐标: [795,919][1049,1001]
14:49:27.961/D: 文字项39: "AuthApp" 坐标: [33,1007][287,1282]
14:49:27.961/D: 文字项40: "AuthApp" 坐标: [74,1023][246,1195]
14:49:27.961/D: 文字项41: "AuthApp" 坐标: [33,1200][287,1282]
14:49:27.961/D: 文字项42: "电话" 坐标: [32,1908][286,2096]
14:49:27.962/D: 文字项43: "电话" 坐标: [73,1924][245,2096]
14:49:27.962/D: 文字项44: "短信" 坐标: [286,1908][540,2096]
14:49:27.962/D: 文字项45: "短信" 坐标: [327,1924][499,2096]
14:49:27.963/D: 文字项46: "浏览器" 坐标: [540,1908][794,2096]
14:49:27.964/D: 文字项47: "浏览器" 坐标: [581,1924][753,2096]
14:49:27.965/D: 文字项48: "相机" 坐标: [794,1908][1048,2096]
14:49:27.967/D: 匹配模式列表: ["*平台列表*", "*今日头条*", "*通道*", "*APP*"]
14:49:27.968/D: 🔍 检查必须关键词通配符匹配: "第1屏" vs "*小红书*"
14:49:27.968/D: 🔍 通配符匹配详情: 文字="第1屏", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.969/D: 🔍 正则匹配结果: false
14:49:27.969/D: 🔍 通配符匹配结果: false
14:49:27.969/D: 🔍 检查必须关键词通配符匹配: "第2屏" vs "*小红书*"
14:49:27.970/D: 🔍 通配符匹配详情: 文字="第2屏", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.970/D: 🔍 正则匹配结果: false
14:49:27.970/D: 🔍 通配符匹配结果: false
14:49:27.971/D: 🔍 检查必须关键词通配符匹配: "米家" vs "*小红书*"
14:49:27.971/D: 🔍 通配符匹配详情: 文字="米家", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.972/D: 🔍 正则匹配结果: false
14:49:27.972/D: 🔍 通配符匹配结果: false
14:49:27.972/D: 🔍 检查必须关键词通配符匹配: "米家" vs "*小红书*"
14:49:27.973/D: 🔍 通配符匹配详情: 文字="米家", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.973/D: 🔍 正则匹配结果: false
14:49:27.974/D: 🔍 通配符匹配结果: false
14:49:27.975/D: 🔍 检查必须关键词通配符匹配: "米家" vs "*小红书*"
14:49:27.976/D: 🔍 通配符匹配详情: 文字="米家", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.978/D: 🔍 正则匹配结果: false
14:49:27.978/D: 🔍 通配符匹配结果: false
14:49:27.979/D: 🔍 检查必须关键词通配符匹配: "音乐" vs "*小红书*"
14:49:27.980/D: 🔍 通配符匹配详情: 文字="音乐", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.981/D: 🔍 正则匹配结果: false
14:49:27.981/D: 🔍 通配符匹配结果: false
14:49:27.982/D: 🔍 检查必须关键词通配符匹配: "音乐" vs "*小红书*"
14:49:27.983/D: 🔍 通配符匹配详情: 文字="音乐", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.983/D: 🔍 正则匹配结果: false
14:49:27.983/D: 🔍 通配符匹配结果: false
14:49:27.984/D: 🔍 检查必须关键词通配符匹配: "音乐" vs "*小红书*"
14:49:27.985/D: 🔍 通配符匹配详情: 文字="音乐", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.986/D: 🔍 正则匹配结果: false
14:49:27.987/D: 🔍 通配符匹配结果: false
14:49:27.988/D: 🔍 检查必须关键词通配符匹配: "笔记" vs "*小红书*"
14:49:27.990/D: 🔍 通配符匹配详情: 文字="笔记", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.991/D: 🔍 正则匹配结果: false
14:49:27.991/D: 🔍 通配符匹配结果: false
14:49:27.992/D: 🔍 检查必须关键词通配符匹配: "笔记" vs "*小红书*"
14:49:27.993/D: 🔍 通配符匹配详情: 文字="笔记", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.994/D: 🔍 正则匹配结果: false
14:49:27.994/D: 🔍 通配符匹配结果: false
14:49:27.995/D: 🔍 检查必须关键词通配符匹配: "笔记" vs "*小红书*"
14:49:27.997/D: 🔍 通配符匹配详情: 文字="笔记", 模式="*小红书*", 正则=".*小红书.*"
14:49:27.998/D: 🔍 正则匹配结果: false
14:49:27.999/D: 🔍 通配符匹配结果: false
14:49:28.000/D: 🔍 检查必须关键词通配符匹配: "小米社区" vs "*小红书*"
14:49:28.000/D: 🔍 通配符匹配详情: 文字="小米社区", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.000/D: 🔍 正则匹配结果: false
14:49:28.001/D: 🔍 通配符匹配结果: false
14:49:28.001/D: 🔍 检查必须关键词通配符匹配: "小米社区" vs "*小红书*"
14:49:28.001/D: 🔍 通配符匹配详情: 文字="小米社区", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.002/D: 🔍 正则匹配结果: false
14:49:28.002/D: 🔍 通配符匹配结果: false
14:49:28.002/D: 🔍 检查必须关键词通配符匹配: "小米社区" vs "*小红书*"
14:49:28.003/D: 🔍 通配符匹配详情: 文字="小米社区", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.003/D: 🔍 正则匹配结果: false
14:49:28.004/D: 🔍 通配符匹配结果: false
14:49:28.005/D: 🔍 检查必须关键词通配符匹配: "小米有品" vs "*小红书*"
14:49:28.006/D: 🔍 通配符匹配详情: 文字="小米有品", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.007/D: 🔍 正则匹配结果: false
14:49:28.007/D: 🔍 通配符匹配结果: false
14:49:28.008/D: 🔍 检查必须关键词通配符匹配: "小米有品" vs "*小红书*"
14:49:28.010/D: 🔍 通配符匹配详情: 文字="小米有品", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.011/D: 🔍 正则匹配结果: false
14:49:28.012/D: 🔍 通配符匹配结果: false
14:49:28.012/D: 🔍 检查必须关键词通配符匹配: "小米有品" vs "*小红书*"
14:49:28.013/D: 🔍 通配符匹配详情: 文字="小米有品", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.013/D: 🔍 正则匹配结果: false
14:49:28.014/D: 🔍 通配符匹配结果: false
14:49:28.014/D: 🔍 检查必须关键词通配符匹配: "热门" vs "*小红书*"
14:49:28.014/D: 🔍 通配符匹配详情: 文字="热门", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.014/D: 🔍 正则匹配结果: false
14:49:28.015/D: 🔍 通配符匹配结果: false
14:49:28.015/D: 🔍 检查必须关键词通配符匹配: "热门" vs "*小红书*"
14:49:28.015/D: 🔍 通配符匹配详情: 文字="热门", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.016/D: 🔍 正则匹配结果: false
14:49:28.016/D: 🔍 通配符匹配结果: false
14:49:28.016/D: 🔍 检查必须关键词通配符匹配: "热门" vs "*小红书*"
14:49:28.017/D: 🔍 通配符匹配详情: 文字="热门", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.017/D: 🔍 正则匹配结果: false
14:49:28.017/D: 🔍 通配符匹配结果: false
14:49:28.018/D: 🔍 检查必须关键词通配符匹配: "小红书" vs "*小红书*"
14:49:28.019/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.020/D: 🔍 正则匹配结果: true
14:49:28.021/D: 🔍 通配符匹配结果: true
14:49:28.021/D: 正在检查文字 "小红书" 是否匹配模式...
14:49:28.022/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*平台列表*", 正则=".*平台列表.*"
14:49:28.022/D: 🔍 正则匹配结果: false
14:49:28.022/D:   模式 "*平台列表*" 匹配结果: false
14:49:28.023/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*今日头条*", 正则=".*今日头条.*"
14:49:28.023/D: 🔍 正则匹配结果: false
14:49:28.024/D:   模式 "*今日头条*" 匹配结果: false
14:49:28.024/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*通道*", 正则=".*通道.*"
14:49:28.025/D: 🔍 正则匹配结果: false
14:49:28.025/D:   模式 "*通道*" 匹配结果: false
14:49:28.025/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*APP*", 正则=".*APP.*"
14:49:28.026/D: 🔍 正则匹配结果: false
14:49:28.026/D:   模式 "*APP*" 匹配结果: false
14:49:28.027/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*复扫*", 正则=".*复扫.*"
14:49:28.027/D: 🔍 正则匹配结果: false
14:49:28.028/D: ✅ 匹配到文字: "小红书" 坐标: [541,445][795,720] 原因: 必须关键词
14:49:28.028/D: 🔍 检查必须关键词通配符匹配: "小红书" vs "*小红书*"
14:49:28.029/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.030/D: 🔍 正则匹配结果: true
14:49:28.030/D: 🔍 通配符匹配结果: true
14:49:28.031/D: 正在检查文字 "小红书" 是否匹配模式...
14:49:28.032/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*平台列表*", 正则=".*平台列表.*"
14:49:28.033/D: 🔍 正则匹配结果: false
14:49:28.033/D:   模式 "*平台列表*" 匹配结果: false
14:49:28.034/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*今日头条*", 正则=".*今日头条.*"
14:49:28.034/D: 🔍 正则匹配结果: false
14:49:28.035/D:   模式 "*今日头条*" 匹配结果: false
14:49:28.035/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*通道*", 正则=".*通道.*"
14:49:28.035/D: 🔍 正则匹配结果: false
14:49:28.036/D:   模式 "*通道*" 匹配结果: false
14:49:28.036/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*APP*", 正则=".*APP.*"
14:49:28.037/D: 🔍 正则匹配结果: false
14:49:28.037/D:   模式 "*APP*" 匹配结果: false
14:49:28.038/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*复扫*", 正则=".*复扫.*"
14:49:28.038/D: 🔍 正则匹配结果: false
14:49:28.039/D: ✅ 匹配到文字: "小红书" 坐标: [582,461][754,633] 原因: 必须关键词
14:49:28.039/D: 🔍 检查必须关键词通配符匹配: "小红书" vs "*小红书*"
14:49:28.041/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.042/D: 🔍 正则匹配结果: true
14:49:28.043/D: 🔍 通配符匹配结果: true
14:49:28.044/D: 正在检查文字 "小红书" 是否匹配模式...
14:49:28.044/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*平台列表*", 正则=".*平台列表.*"
14:49:28.045/D: 🔍 正则匹配结果: false
14:49:28.045/D:   模式 "*平台列表*" 匹配结果: false
14:49:28.046/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*今日头条*", 正则=".*今日头条.*"
14:49:28.047/D: 🔍 正则匹配结果: false
14:49:28.047/D:   模式 "*今日头条*" 匹配结果: false
14:49:28.048/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*通道*", 正则=".*通道.*"
14:49:28.048/D: 🔍 正则匹配结果: false
14:49:28.048/D:   模式 "*通道*" 匹配结果: false
14:49:28.049/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*APP*", 正则=".*APP.*"
14:49:28.050/D: 🔍 正则匹配结果: false
14:49:28.050/D:   模式 "*APP*" 匹配结果: false
14:49:28.051/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*复扫*", 正则=".*复扫.*"
14:49:28.051/D: 🔍 正则匹配结果: false
14:49:28.052/D: ✅ 匹配到文字: "小红书" 坐标: [541,638][795,720] 原因: 必须关键词
14:49:28.053/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 3 个结果====🔚🔚🔚
14:49:28.053/D: 🔛🔛🔛====点击XML关键词开始====🔛🔛🔛
14:49:28.054/D: 要点击的关键词模式: "*小红书*"
14:49:28.056/D: 🔍 通配符匹配详情: 文字="小红书", 模式="*小红书*", 正则=".*小红书.*"
14:49:28.056/D: 🔍 正则匹配结果: true
14:49:28.057/D: ✅ 找到匹配项: "小红书" 匹配模式: "*小红书*" 匹配类型: 模糊匹配
14:49:28.057/D: 🎯 准备点击: "小红书" 坐标: (668, 583) 偏移: (0, 0) 次数: 1 间隔: 50ms
14:49:28.058/D: su -c 'input tap 667 585.5'
14:49:28.136/D: ✅ 点击执行完成
14:49:28.136/D: 🔚🔚🔚====点击XML关键词结束====🔚🔚🔚
14:49:29.137/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
14:49:29.137/D: 参数：必须关键词="拒绝", 匹配关键词="本次运行允许|仅在使用中允许", 匹配数量=2, 排除关键词=""
14:49:29.137/D: 使用缓存的XML内容
14:49:29.137/D: 开始获取界面 XML...
14:49:30.671/D: 成功获取 XML 文件，大小: 17150 字节
14:49:30.672/D: 开始解析XML获取所有文字...
14:49:30.693/V: [$cwd/main不自动重启.js] 运行结束 (用时 103.910 秒)

14:49:30.705/D: 脚本即将退出，执行清理工作...
14:49:30.705/D: 清理工作完成
14:49:30.706/D: 脚本即将退出，执行清理工作...
14:49:30.707/D: 清理工作完成
14:49:30.708/D: 脚本即将退出，执行最终清理工作
14:49:30.710/D: 退出前垃圾回收完成