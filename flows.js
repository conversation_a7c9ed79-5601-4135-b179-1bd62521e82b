/**
 * 流程管理模块 - 定义各种操作流程
 * 作者: Claude
 * 日期: 2025-07-01
 */

let 与梦注册小红书失败计数 = 0

// 引入依赖 - 兼容打包环境
// 强制重新加载模块，清除缓存
try {
    delete require.cache[require.resolve('./DeviceOperation.js')];
} catch (e) {
    // 忽略缓存清除错误
}

var DeviceOperation;
try {
    DeviceOperation = require('./DeviceOperation.js');
} catch (e) {
    try {
        DeviceOperation = require('DeviceOperation.js');
    } catch (e2) {
        console.error("flows.js: DeviceOperation模块加载失败:", e.message);
        throw e;
    }
}

// 导入OCR模块 - 兼容打包环境
// 强制重新加载OCR模块，清除缓存
try {
    delete require.cache[require.resolve('./ocr.js')];
} catch (e) {
    // 忽略缓存清除错误
}

var OCR;
try {
    OCR = require('./ocr.js');
} catch (e) {
    try {
        OCR = require('ocr.js');
    } catch (e2) {
        console.error("flows.js: OCR模块加载失败:", e.message);
        // 创建空的OCR模块
        OCR = {
            获取屏幕文字信息: function () { return []; },
            点击指定关键词: function () { return false; },
            点击指定文字坐标: function () { return false; },
            长按指定关键词: function () { return false; }
        };
    }
}

// 导入xiaohongshu模块 - 兼容打包环境
// 强制重新加载xiaohongshu模块，清除缓存
try {
    delete require.cache[require.resolve('./xiaohongshu.js')];
} catch (e) {
    // 忽略缓存清除错误
}

var xiaohongshu模块;
try {
    xiaohongshu模块 = require('./xiaohongshu.js');
} catch (e) {
    try {
        xiaohongshu模块 = require('xiaohongshu.js');
    } catch (e2) {
        console.error("flows.js: xiaohongshu模块加载失败:", e.message);
        // 创建空的xiaohongshu模块
        xiaohongshu模块 = {
            是否在主界面: function () { return false; }
        };
    }
}

/**
 * 执行主页键操作
 * @returns {boolean} 是否成功
 */
function 主页键() {
    console.log("执行主页键操作");
    return DeviceOperation.主页键();
}

/**
 * 执行返回操作
 * @returns {boolean} 是否成功
 */
function 返回() {
    console.log("执行返回操作");
    return DeviceOperation.返回();
}

/**
 * 执行滑动操作
 * @param {number} 起点X - 起点X坐标
 * @param {number} 起点Y - 起点Y坐标
 * @param {number} 终点X - 终点X坐标
 * @param {number} 终点Y - 终点Y坐标
 * @param {Object} 选项 - 滑动选项
 * @returns {boolean} 是否成功
 */
function 滑动(起点X, 起点Y, 终点X, 终点Y, 选项 = {}) {
    console.log(`执行滑动操作: (${起点X}, ${起点Y}) -> (${终点X}, ${终点Y})`);

    // 执行滑动
    var 成功 = DeviceOperation.滑动(起点X, 起点Y, 终点X, 终点Y, 选项);

    // 如果指定了等待时间，等待指定时间
    if (成功 && 选项.等待时间 > 0) {
        console.log(`滑动成功，等待 ${选项.等待时间}ms`);
        DeviceOperation.sleep(选项.等待时间);
    }

    return 成功;
}

/**
 * 执行方向滑动操作
 * @param {string} 方向 - 滑动方向，"上"、"下"、"左"、"右"
 * @param {string|number} 距离 - 滑动距离，"短"、"中"、"长"或具体像素值
 * @param {Object} 选项 - 滑动选项
 * @returns {boolean} 是否成功
 */
function 方向滑动(方向, 距离, 选项 = {}) {
    console.log(`执行${方向}滑动, 距离: ${距离}`);

    // 获取屏幕尺寸
    var 屏幕宽度 = typeof device !== 'undefined' ? device.width : 1080;
    var 屏幕高度 = typeof device !== 'undefined' ? device.height : 2340;

    // 计算滑动距离
    var 实际距离 = 0;
    if (方向 === "上" || 方向 === "下") {
        if (距离 === "短") {
            实际距离 = Math.floor(屏幕高度 * 0.3);
        } else if (距离 === "中") {
            实际距离 = Math.floor(屏幕高度 * 0.5);
        } else if (距离 === "长") {
            实际距离 = Math.floor(屏幕高度 * 0.7);
        } else {
            实际距离 = parseInt(距离) || Math.floor(屏幕高度 * 0.5);
        }
    } else {
        if (距离 === "短") {
            实际距离 = Math.floor(屏幕宽度 * 0.3);
        } else if (距离 === "中") {
            实际距离 = Math.floor(屏幕宽度 * 0.5);
        } else if (距离 === "长") {
            实际距离 = Math.floor(屏幕宽度 * 0.7);
        } else {
            实际距离 = parseInt(距离) || Math.floor(屏幕宽度 * 0.5);
        }
    }

    // 计算起点和终点坐标
    var 起点X = 0, 起点Y = 0, 终点X = 0, 终点Y = 0;

    if (方向 === "上") {
        起点X = Math.floor(屏幕宽度 / 2);
        起点Y = Math.floor(屏幕高度 * 0.7);
        终点X = 起点X;
        终点Y = 起点Y - 实际距离;
    } else if (方向 === "下") {
        起点X = Math.floor(屏幕宽度 / 2);
        起点Y = Math.floor(屏幕高度 * 0.3);
        终点X = 起点X;
        终点Y = 起点Y + 实际距离;
    } else if (方向 === "左") {
        起点X = Math.floor(屏幕宽度 * 0.7);
        起点Y = Math.floor(屏幕高度 / 2);
        终点X = 起点X - 实际距离;
        终点Y = 起点Y;
    } else if (方向 === "右") {
        起点X = Math.floor(屏幕宽度 * 0.3);
        起点Y = Math.floor(屏幕高度 / 2);
        终点X = 起点X + 实际距离;
        终点Y = 起点Y;
    }

    console.log(`滑动坐标: (${起点X}, ${起点Y}) -> (${终点X}, ${终点Y})`);

    // 执行滑动
    return 滑动(起点X, 起点Y, 终点X, 终点Y, 选项);
}

/**
 * 执行点击操作
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @param {Object} 选项 - 点击选项
 * @returns {boolean} 是否成功
 */
function 点击(x, y, 选项 = {}) {
    console.log(`执行点击操作: (${x}, ${y})`);

    // 执行点击
    var 成功 = DeviceOperation.点击(x, y, 选项);

    // 如果指定了等待时间，等待指定时间
    if (成功 && 选项.等待时间 > 0) {
        console.log(`点击成功，等待 ${选项.等待时间}ms`);
        DeviceOperation.sleep(选项.等待时间);
    }

    return 成功;
}

function 备份还原() {

    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    console.log("备份还原  主页键");
    DeviceOperation.主页键();
    DeviceOperation.主页键();
    sleep(2000);
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {
            // 在每个关键步骤前检查权限


            // 查找设置和小红书/相册
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                let result = shell("am start -a android.settings.SETTINGS", true);
                强制截图 = null;
                sleep(1000);
                break;
                结果 = OCR.获取屏幕文字信息("**设置*|*設置*", "小红书|相册", 2, 强制截图)
                if (结果.length > 0) {
                    console.log("检测到设置和小红书/相册");
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("1 查找设置和小红书/相册");
                强制截图 = false;
            }

            // 检查权限


            // 查找备份相关选项
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("我的?备", "蓝牙|*共享|*个性*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("2 查找备份相关选项");
                强制截图 = false;
            }

            // 检查权限


            // 查找版本、型号等信息
            console.log("查找版本、型号等信息");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("我的?备", "*版本*|*型号*|*内存*|*像素*|*尺寸*", 2, "蓝牙|*共享|*个性*|*在恢复*", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    console.log("3 找到匹配项，向上滑动");
                    console.log(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 500);
                    DeviceOperation.滑动(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("3 查找版本、型号等信息");
                强制截图 = false;
            }

            // 检查权限


            // 查找备份与恢复
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("备份与恢复", "我的?备|*参数*|*换机*|*出厂*|*应用*", 3, "*版本*|*名称*|*空间*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("4 未找到备份与恢复选项，流程失败");
                强制截图 = false;
            }

            // 检查权限


            // 步骤5: 点击手机备份恢复
            console.log("步骤5: 点击手机备份恢复");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("手机备份恢复", "备份与恢复|本地|*电脑*|*服务*|*帮助*", 3, "我的?备|*参数*|*换机*|*出厂*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("5 未找到手机备份恢复选项，流程失败");
                强制截图 = false;
            }

            // 检查权限


            // 步骤6: 点击手机恢复选项
            console.log("步骤6: 点击手机恢复选项");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*手机备份*", "*手机恢复*|*加载中*|立即备份|*联系人*|*系统应用*|*第三方*", 3, "本地|*服务*|*帮助*|*日*|*月*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    console.log("6 找到手机恢复选项，使用偏移点击");
                    OCR.点击指定文字坐标(结果, "", 1, "", 250, 5);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("6 未找到手机恢复选项，尝试重试...");
                强制截图 = false;
            }

            // 检查权限


            // 步骤7: 选择备份记录
            console.log("步骤7: 选择备份记录");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*月*日*|*个程序*", "手机备份|手机恢复", 1, "立即备份|*联系人*|*系统应用*|*第三方*|*申*|*卯*|*酉*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("7 未找到备份记录，尝试重试...");
                强制截图 = false;
            }

            // 检查权限


            // 步骤8: 点击立即恢复
            console.log("步骤8: 点击立即恢复");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("立即恢复", "手机恢复|*第三方*", 2, "", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("8 未找到立即恢复按钮，流程可能失败");
                强制截图 = false;
            }

            // 检查权限


            // 步骤9: 等待恢复过程
            console.log("步骤9: 等待恢复过程");
            let 恢复完成 = false;
            let 等待时间 = 0;
            let 最大等待时间 = 10;
            let 检查间隔 = 1000; // 每3秒检查一次

            while (等待时间 < 最大等待时间) {
                强制截图 = null
                // 检查是否包含"正在恢复"文字
                结果 = OCR.获取屏幕文字信息("手机恢复", "*正在恢复*", 2, "*完成*", 强制截图);
                if (结果.length > 0) {
                    console.log("9 恢复过程仍在进行中...");
                } else {
                    break;
                }
                sleep(检查间隔);
                等待时间 += 检查间隔;
                console.log(`已等待 ${等待时间 / 1000} 秒，继续等待恢复完成...`);
            }

            // 检查权限


            // 步骤10: 确认恢复完成
            console.log("步骤10: 确认恢复完成");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("完成", "手机恢复|*条记录*", 2, "正在恢复|*重启*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "完成");
                    sleep(1000);
                    console.log("还原备份操作已完成，流程结束");
                    检测成功 = true;
                    return true;
                }
            }
            if (!检测成功) {
                强制截图 = null;
                console.log("10 未找到完成按钮，流程可能未正常结束");
            }
        }
        sleep(1000);
    } while (true);
}


function 删除第一个备份() {

    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    //home();
    //sleep(2000);
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {
            // 检查权限


            // 步骤7: 选择备份记录
            console.log("长按备份记录");
            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*月*日*|*个程序*", "手机备份|手机恢复", 1, "立即备份|*联系人*|*系统应用*|*第三方*|*申*|*卯*|*酉*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.长按指定关键词(结果, "*月*日*|*个程序*")
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("未找到记录，尝试重试...");
                强制截图 = false;
            }

            // 检查权限


            // 步骤: 选择备份记录
            console.log("删除备份");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("删除", "*月*日*|*个程序*|手机备份|手机恢复", 2, "立即备份|*联系人*|*系统应用*|*第三方*|*申*|*卯*|*酉*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "删除");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("未找到记录，尝试重试...");
                强制截图 = false;
            }
            // 确定删除
            console.log("确定删除备份");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("确定", "取消|删除备份|*月*日*|*个程序*|手机备份|手机恢复", 3, "立即备份|*联系人*|*系统应用*|*第三方*|*申*|*卯*|*酉*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "确定");
                    sleep(1000);
                    console.log("删除备份完成");
                    检测成功 = true;
                    return true;
                }
            }
            if (!检测成功) {
                console.log("未找到记录，尝试重试...");
                强制截图 = false;
            }

            // 在每个关键步骤前检查权限


            // 查找设置和小红书/相册
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                let result = shell("am start -a android.settings.SETTINGS", true);
                强制截图 = null;
                sleep(1000);
                break;
                结果 = OCR.获取屏幕文字信息("*设置*|*設置*", "小红书|相册", 2, 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("1 查找设置和小红书/相册");
                强制截图 = false;
            }

            // 检查权限


            // 查找备份相关选项
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("我的?备", "蓝牙|*共享|*个性*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("2 查找备份相关选项");
                强制截图 = false;
            }

            // 检查权限


            // 查找版本、型号等信息
            console.log("查找版本、型号等信息");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("我的?备", "*版本*|*型号*|*内存*|*像素*|*尺寸*", 2, "蓝牙|*共享|*个性*|*在恢复*", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    console.log("3 找到匹配项，向上滑动");
                    console.log(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 500);
                    DeviceOperation.滑动(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("3 查找版本、型号等信息");
                强制截图 = false;
            }

            // 检查权限


            // 查找备份与恢复
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("备份与恢复", "我的?备|*参数*|*换机*|*出厂*|*应用*", 3, "*版本*|*名称*|*空间*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("4 未找到备份与恢复选项，流程失败");
                强制截图 = false;
            }

            // 检查权限


            // 步骤5: 点击手机备份恢复
            console.log("步骤5: 点击手机备份恢复");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("手机备份恢复", "备份与恢复|本地|*电脑*|*服务*|*帮助*", 3, "我的?备|*参数*|*换机*|*出厂*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("5 未找到手机备份恢复选项，流程失败");
                强制截图 = false;
            }

            // 检查权限


            // 步骤6: 点击手机恢复选项
            console.log("步骤6: 点击手机恢复选项");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*手机备份*", "*手机恢复*|*加载中*|立即备份|*联系人*|*系统应用*|*第三方*", 3, "本地|*服务*|*帮助*|*日*|*月*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    console.log("6 找到手机恢复选项，使用偏移点击");
                    OCR.点击指定文字坐标(结果, "", 1, "", 250, 5);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("6 未找到手机恢复选项，尝试重试...");
                强制截图 = false;
            }

            // 检查权限


            // 步骤8: 点击立即恢复
            console.log("步骤8: 点击立即恢复");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("立即恢复", "手机恢复|*第三方*", 2, "", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("8 未找到立即恢复按钮，流程可能失败");
                强制截图 = false;
            }

            // 检查权限


            // 步骤10: 确认恢复完成
            console.log("步骤10: 确认恢复完成");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("完成", "手机恢复|*条记录*", 1, "正在恢复|*重启*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "完成");
                    sleep(1000);
                    console.log("还原备份操作已完成，流程结束");
                    检测成功 = true;
                    return true;
                }
            }
            if (!检测成功) {
                强制截图 = null;
                console.log("13 未找到完成按钮，流程可能未正常结束");
            }

        }
        sleep(1000);
    } while (true);
}


function 恢复出厂设置() {
    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    DeviceOperation.主页键();
    DeviceOperation.主页键();
    sleep(2000);
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {
            // 在每个关键步骤前检查权限


            // 查找设置和小红书/相册
            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                let result = shell("am start -a android.settings.SETTINGS", true);
                强制截图 = null;
                sleep(1000);
                break;
                结果 = OCR.获取屏幕文字信息("*设置*|*設置*", "小红书|相册", 2, 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("1 查找设置和小红书/相册");
                强制截图 = false;
            }

            // 检查权限


            // 查找备份相关选项
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("我的?备", "蓝牙|*共享|*个性*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("2 查找备份相关选项");
                强制截图 = false;
            }

            // 检查权限


            // 查找版本、型号等信息
            console.log("查找版本、型号等信息");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("我的?备", "*版本*|*型号*|*内存*|*像素*|*尺寸*", 2, "蓝牙|*共享|*个性*|*在恢复*", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    console.log("3 找到匹配项，向上滑动");
                    console.log(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 500);
                    DeviceOperation.滑动(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("3 查找版本、型号等信息");
                强制截图 = false;
            }

            // 检查权限


            // 
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("恢复出厂设置", "我的?备|*参数*|*换机*|*备份*|*应用*", 3, "*版本*|*名称*|*空间*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "恢复出厂设置");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("未找到选项，流程失败");
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("清除所有数据", "恢复出厂设置|*账号*|*联系人*|*图片*|*应用*", 3, "*全部参数*|*立即备份*|*如果您*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "清除所有数据");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("未找到选项，流程失败");
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("立即备份", "恢复出厂设置|*账号*|*联系人*|*图片*|*应用*", 3, "*清除所有数据*|*以上所有*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    console.log("恢复出厂设置");
                    OCR.点击指定关键词(结果, "立即备份", "", -350, 0);//偏移点击 恢复出厂设置
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("未找到选项，流程失败");
                强制截图 = false;
            }
            // 下一步确定
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*下一步*", "*取消*|*警告*|*永久*|*数据*|*不限*|*保存至*|*执行*|*的所有*", 4, "*立即备份*|*确定*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "*下一步*");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("未找到选项，流程失败");
                强制截图 = false;
            }
            // 确定
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("确定", "*取消*|*警告*|*不可*|*数据*|*备份*|*清除*|*已经*|*的所有*", 4, "*立即备份*|*下一步*", 强制截图);
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "确定");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("未找到选项，流程失败");
                强制截图 = false;
            }
        }
        sleep(1000);
    } while (true);
}


function 重启飞行模式() {
    return 重启飞行模式shell();


    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    DeviceOperation.主页键();
    DeviceOperation.主页键();
    sleep(2000);
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {
            // 在每个关键步骤前检查权限


            // 查找设置和小红书/相册
            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*设置*|*設置*", "小红书|相册", 2, 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定文字坐标(结果, "", 1);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("1 查找设置和小红书/相册");
                强制截图 = false;
            }

            // 检查权限


            // 查找备份相关选项
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*连*与共享*", "蓝牙|*我的*|*个性*|*设置*|*設置*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "*连*与共享*");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                console.log("查找相关选项失败");
                强制截图 = false;
            }

            // 检查权限


            // 查找版本、型号等信息
            console.log("查找版本、型号等信息");
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("飞行模式", "*连接与共享*|*热点*|*互传*|*打印*|*投屏*", 2, "蓝牙|*个性*|*在恢复*", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "飞行模式");
                    sleep(4000);
                    OCR.点击指定关键词(结果, "飞行模式");
                    //sleep(1000);
                    检测成功 = true;
                    return true;
                }
            }
            if (!检测成功) {
                console.log("3 查找版本、型号等信息");
                强制截图 = false;
            }
        }
        sleep(1000);
    } while (true);
}

function 重启飞行模式shell() {
    console.log("开始重启飞行模式...");
    toast("开始重启飞行模式...");
    do {
        try {
            // 打开飞行模式
            打开飞行模式shell()
            // 等待5秒
            console.log("等待5秒...");
            sleep(5000);

            // 关闭飞行模式
            关闭飞行模式shell()
            console.log("🎉 飞行模式重启完成");
            toast("🎉 飞行模式重启完成");
            sleep(3000)
            return true;
        } catch (e) {
            console.error("❌ 飞行模式重启失败: " + e.message);
            console.log("等待5秒后重试...");
            toast("❌ 飞行模式重启失败,等待5秒后重试...");
            toast("等待5秒后重试...")
            sleep(5000);
            //return false;
        }
    } while (true);
}


function 打开飞行模式shell() {
    try {
        // 打开飞行模式
        console.log("正在打开飞行模式...");
        toast("正在打开飞行模式...");
        shell("settings put global airplane_mode_on 1", true);
        shell("am broadcast -a android.intent.action.AIRPLANE_MODE --ez state true", true);
        return true;
    } catch (e) {
        console.error("❌ 飞行模式打开失败: " + e.message);
        return false;
    }
}


function 关闭飞行模式shell() {
    try {
        // 关闭飞行模式
        console.log("正在关闭飞行模式...");
        toast("正在关闭飞行模式...");
        shell("settings put global airplane_mode_on 0", true);
        shell("am broadcast -a android.intent.action.AIRPLANE_MODE --ez state false", true);
        return true;
    } catch (e) {
        console.error("❌ 飞行模式关闭失败: " + e.message);
        return false;
    }
}





function 备份工具箱还原备份() {

    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 3;
    DeviceOperation.主页键();
    DeviceOperation.主页键();
    sleep(2000);
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {
            // 在每个关键步骤前检查权限

            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("请输入", "移除备注|取消|确定", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    sleep(100)
                    DeviceOperation.返回();
                    sleep(1000);
                    DeviceOperation.返回();
                    sleep(1000);
                    DeviceOperation.返回();
                    sleep(1000);
                    检测成功 = true;
                }
                break;
            }
            if (!检测成功) {
                强制截图 = false;
            }

            // 查找备份工具箱和小红书/相册
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("备份工具箱", "小红书|相册", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "备份工具箱");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限
            // 查找设置和小红书/相册
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("备份工具箱", "*备份目录*|*机主*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "备份工具箱", "", 580);
                    sleep(1000);
                    //输入 小红书 搜索
                    // 尝试输入xingin
                    DeviceOperation.输入文本("xingin", { 清除现有文本: true });
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("小红书", "*个备份*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "小红书|*个备份*");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("管理", "*应用信息*|小红书|*备份*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "管理");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("恢复", "*覆盖备份*|小红书|删除|*user*|*data*|*apk*", 3, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "恢复");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;
            for (let j = 0; j < 20; j++) {
                结果 = OCR.获取屏幕文字信息("正在恢复", "开始*|恢复*", 2, "恢复成功|*完成*", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    sleep(2000);
                } else {
                    强制截图 = null;
                    检测成功 = true;
                    break;
                }
            }

            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("关闭", "恢复成功|*完成*|*user*|*data*", 3, "正在恢复|删除", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "关闭");
                    sleep(1000);
                    检测成功 = true;
                    备份工具箱删除备份()
                    sleep(100)
                    DeviceOperation.返回();
                    sleep(1000)
                    DeviceOperation.返回();
                    sleep(1000)

                    return true;

                }
            }
        }
        sleep(1000);
    } while (true);
}

function 备份工具箱删除备份() {

    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    let 删除标记 = 0
    // DeviceOperation.主页键();
    // DeviceOperation.主页键();
    // sleep(2000);
    console.log("备份工具箱删除备份");
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {

            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("管理", "*应用信息*|小红书|*备份*|*备注*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "管理");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限


            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("请输入", "移除备注|取消|确定", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    sleep(100)
                    DeviceOperation.返回();
                    sleep(1000);
                }
                结果 = OCR.获取屏幕文字信息("*删除*", "*覆盖备份*|小红书|恢复|*user*|*data*|*apk*", 3, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    // 使用点击次数参数，点击2次
                    OCR.点击指定关键词(结果, "*删除*", "", 0, 0, 2, 25);
                    sleep(3000);
                    检测成功 = true;
                    删除标记 = 删除标记 + 1 //删除标记
                    console.log("删除标记:" + 删除标记);
                    toast("删除标记:" + 删除标记);
                    continue //继续检查
                    return true
                    break;
                }
                if (删除标记 > 0) {  //之前删除过.并且没有找到删除按钮.说明已经删完了
                    console.log("删除完成");
                    toast("删除完成");
                    DeviceOperation.返回();
                    return true
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("请输入", "移除备注|取消|确定", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    sleep(100)
                    DeviceOperation.返回();
                    sleep(1000);
                    DeviceOperation.返回();
                    sleep(1000);
                    DeviceOperation.返回();
                    sleep(1000);
                    return true
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            // 查找备份工具箱和小红书/相册
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("备份工具箱", "小红书|相册", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "备份工具箱");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限
            // 查找设置和小红书/相册
            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("备份工具箱", "*备份目录*|*机主*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "备份工具箱", "", 580);
                    sleep(1000);
                    //输入 小红书 搜索
                    // 尝试输入xingin
                    DeviceOperation.输入文本("xingin", { 清除现有文本: true });
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("小红书", "*个备份*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "小红书|*个备份*");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
        }
        sleep(1000);
    } while (true);
}



function 改机工具_汽水音乐() {

    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    //DeviceOperation.主页键();
    //DeviceOperation.主页键();
    //sleep(2000);
    打开飞行模式shell()
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {

            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                shell("monkey -p com.luna.music -c android.intent.category.LAUNCHER 1", true);
                sleep(1000)
                break
                结果 = OCR.获取屏幕文字信息("汽水音乐", "小红书|相册", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "汽水音乐");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;//text('变更机型')
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*一键三连*|*合并下面*|*三步操作*", "安卓玩机助手|*管理与数据*|还原更改|变更机型|超级清理", 3, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "*一键三连*|*合并下面*|*三步操作*");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false; //text('取消')
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("执行重启", "*并清理完*|取消|*手机重启*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "执行重启");
                    sleep(1000);
                    检测成功 = true;
                    return true;
                    break;
                }
            }

        }
        sleep(1000);
    } while (true);
}




function 改机工具_阿帕奇() {

    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    shell("am force-stop me.bmax.apatch", true);//关闭APatch
    DeviceOperation.主页键();
    DeviceOperation.主页键();
    sleep(2000);
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
        }
        for (let i = 0; i < 1; i++) {

            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("APatch", "小红书|相册", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "APatch");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false;//text('变更机型')
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("主页", "APatch|*生效中*|*系统版本*|*内核版本*|*系统指纹*", 3, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "主页", "小红书|相册", 690, 0);
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }
            // 检查权限

            检测成功 = false; //text('取消')
            for (let j = 0; j < 检测次数; j++) {
                结果 = OCR.获取屏幕文字信息("*机型更改*|*N9760*", "系统模块|卸载|*伪装机型*|*机型模块*", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    OCR.点击指定关键词(结果, "*机型更改*|*N9760*", "", 642);
                    sleep(5000);
                    OCR.点击指定关键词(结果, "*机型更改*|*N9760*", "", 642);
                    sleep(5000);
                    检测成功 = true;

                    // 重启手机
                    shell("reboot", true);

                    return true;
                }
            }
        }
        sleep(1000);
    } while (true);
}


function 注册账号_与梦() {
    重启飞行模式shell()
    小红书_进入微信登录页面()
    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 6;
    let 删除标记 = 0
    let 已点击通道 = false
    console.log("备份工具箱删除备份");
    do {
        if (与梦注册小红书失败计数 > 1) {
            return false;
        }
        计数 = 计数 + 1
        if (计数 > 16) {
            计数 = 0
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.返回();
            sleep(100)
            DeviceOperation.主页键();
            已点击通道 = false
        } else if (计数 == 8) {
            重启飞行模式shell()
        }
        for (let i = 0; i < 1; i++) {
            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                shell("monkey -p com.wb.www.yytt -c android.intent.category.LAUNCHER 1", true);
                强制截图 = null;
                sleep(1000);
                break;
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = DeviceOperation.获取XML文字信息("*小红书*", "*平台列表*|*今日头条*|*通道*|*APP*", 3, "*复扫*", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    DeviceOperation.点击XML关键词(结果, "*小红书*");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                if (已点击通道) {
                    break;
                }
                结果 = DeviceOperation.获取XML文字信息("通道①", "小红书|*复扫*|*微信*|*通道*|*授权*|扫码只用于授权，不会登录你的 iPad 微信", 3, "*今日头条*", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    DeviceOperation.点击XML关键词(结果, "通道①");
                    sleep(10000);
                    检测成功 = true;
                    已点击通道 = true;
                    计数 = 0
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = DeviceOperation.获取XML文字信息("拒绝", "本次运行允许|仅在使用中允许", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    DeviceOperation.点击XML关键词(结果, "拒绝");
                    sleep(1000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = DeviceOperation.获取XML文字信息("账号下线提示", "", 1, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    sleep(1000);
                    检测成功 = true;
                    已点击通道 = false
                    与梦注册小红书失败计数++
                    shell("pm clear com.xingin.xhs", true);
                    console.log("小红书APP账号已下线，清空小红书APP数据")
                    toast("小红书APP账号已下线，清空小红书APP数据")
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            if (xiaohongshu模块.是否在主界面()) {
                console.log("✅ 检测到已在小红书主界面");
                toast("注册账号", "检测到已在小红书主界面")
                return true;
            }
        }
        sleep(1000);
    } while (true);


}



function 小红书_进入微信登录页面() {
    let 计数 = 0
    let 结果
    let 强制截图 = null
    let 检测次数 = 2;
    let 删除标记 = 0
    shell("pm clear com.xingin.xhs", true);
    sleep(2000);
    console.log("备份工具箱删除备份");
    do {
        计数 = 计数 + 1
        if (计数 > 10) {
            计数 = 0
            shell("pm clear com.xingin.xhs", true);
            sleep(2000);
        }
        for (let i = 0; i < 1; i++) {
            let 检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                shell("monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1", true);
                强制截图 = null;
                sleep(3000);
                break;
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = DeviceOperation.获取XML文字信息("同意", "不同意|个人信息保护提示", 3, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    DeviceOperation.点击XML关键词(结果, "同意");
                    sleep(2000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = DeviceOperation.获取XML文字信息("同意并继续", "温馨提示|放弃使用", 3, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    DeviceOperation.点击XML关键词(结果, "同意并继续");
                    sleep(2000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = DeviceOperation.获取XML文字信息("跳过", "已有账号，去登录|选择*个兴趣", 2, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    DeviceOperation.点击XML关键词(结果, "跳过");
                    sleep(3000);
                    检测成功 = true;
                    break;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }

            检测成功 = false;
            for (let j = 0; j < 检测次数; j++) {
                结果 = DeviceOperation.获取XML文字信息("微信登录", "", 1, "", 强制截图)
                if (结果.length > 0) {
                    强制截图 = null;
                    //DeviceOperation.点击XML关键词(结果, "微信登录");
                    sleep(1000);
                    检测成功 = true;
                    return true;
                }
            }
            if (!检测成功) {
                强制截图 = false;
            }


        }
        sleep(1000);
    } while (true);


}





// 正确导出流程函数
module.exports = {
    主页键,
    返回,
    滑动,
    方向滑动,
    点击,
    备份还原,
    删除第一个备份,
    恢复出厂设置,
    重启飞行模式,
    备份工具箱还原备份,
    备份工具箱删除备份,
    改机工具_汽水音乐,
    改机工具_阿帕奇,
    注册账号_与梦
};

//备份还原()
//删除第一个备份()
//恢复出厂设置()

// 如未定义恢复出厂设置，补充空实现
// if (typeof 恢复出厂设置 !== 'function') {
//     function 恢复出厂设置() {
//         console.log('恢复出厂设置函数未实现');
//     }
// }
